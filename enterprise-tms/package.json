{"name": "enterprise-tms", "version": "1.0.0", "description": "Enterprise Tender Management System with Integrated Bidding Dashboard", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-query": "^5.56.2", "@tanstack/react-query-devtools": "^5.56.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.5.4", "lucide-react": "^0.441.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "tailwind-merge": "^2.5.2", "zustand": "^4.5.5"}, "devDependencies": {"@types/react": "^18.3.8", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.6.0", "@typescript-eslint/parser": "^8.6.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.1.1", "@vitest/ui": "^2.1.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.6.2", "vite": "^5.4.8", "vitest": "^2.1.1"}, "keywords": ["tender-management", "bidding-dashboard", "enterprise", "react", "typescript", "tailwindcss", "shadcn-ui", "ai-powered", "collaboration", "analytics"], "author": "BidBees Enterprise Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bidbees/enterprise-tms"}, "bugs": {"url": "https://github.com/bidbees/enterprise-tms/issues"}, "homepage": "https://github.com/bidbees/enterprise-tms#readme"}