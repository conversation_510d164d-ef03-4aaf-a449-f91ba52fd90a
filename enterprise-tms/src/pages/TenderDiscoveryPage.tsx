import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Map, 
  List, 
  Filter, 
  Search, 
  Download,
  RefreshCw,
  Settings,
  BookmarkPlus,
  Eye,
  Target
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import TenderDiscoveryMap from '../components/tenders/TenderDiscoveryMap';
import TenderListView from '../components/tenders/TenderListView';
import { tenderService } from '../services/api';
import type { TenderData } from '../lib/supabase';

interface FilterState {
  searchQuery: string;
  categories: string[];
  statuses: string[];
  valueRange: [number, number];
  dateRange: {
    from: string;
    to: string;
  };
  location: string;
}

const TENDER_CATEGORIES = [
  'construction',
  'it_services',
  'consulting',
  'supplies',
  'maintenance',
  'professional_services'
];

const TENDER_STATUSES = [
  'open',
  'published',
  'closed',
  'awarded'
];

const TenderDiscoveryPage: React.FC = () => {
  const [selectedTenders, setSelectedTenders] = useState<string[]>([]);
  const [selectedTender, setSelectedTender] = useState<TenderData | null>(null);
  const [activeView, setActiveView] = useState<'map' | 'list'>('map');
  const [showFilters, setShowFilters] = useState(false);
  const [showTenderDetails, setShowTenderDetails] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    open: 0,
    selected: 0,
    totalValue: 0
  });

  const [filters, setFilters] = useState<FilterState>({
    searchQuery: '',
    categories: [],
    statuses: ['open', 'published'],
    valueRange: [0, 100000000],
    dateRange: {
      from: '',
      to: ''
    },
    location: ''
  });

  useEffect(() => {
    loadStats();
  }, [filters]);

  useEffect(() => {
    setStats(prev => ({ ...prev, selected: selectedTenders.length }));
  }, [selectedTenders]);

  const loadStats = async () => {
    try {
      const tenders = await tenderService.getAllTenders({
        category: filters.categories.length > 0 ? filters.categories[0] : undefined,
        status: filters.statuses.length > 0 ? filters.statuses[0] : undefined,
        location: filters.location || undefined,
        minValue: filters.valueRange[0],
        maxValue: filters.valueRange[1],
        dateFrom: filters.dateRange.from || undefined,
        dateTo: filters.dateRange.to || undefined
      });

      const totalValue = tenders.reduce((sum, tender) => sum + (tender.estimated_value || 0), 0);
      const openTenders = tenders.filter(t => t.status === 'open' || t.status === 'published').length;

      setStats({
        total: tenders.length,
        open: openTenders,
        selected: selectedTenders.length,
        totalValue
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleTenderSelect = (tender: TenderData) => {
    setSelectedTender(tender);
    setShowTenderDetails(true);
  };

  const handleTenderToggle = (tenderId: string) => {
    setSelectedTenders(prev => {
      if (prev.includes(tenderId)) {
        return prev.filter(id => id !== tenderId);
      } else {
        return [...prev, tenderId];
      }
    });
  };

  const handleStartBid = (tender: TenderData) => {
    // Navigate to bid creation page
    console.log('Starting bid for tender:', tender.id);
    // This would typically navigate to a bid creation page
  };

  const handleExportSelected = () => {
    if (selectedTenders.length === 0) return;
    
    // Export selected tenders to CSV or PDF
    console.log('Exporting selected tenders:', selectedTenders);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const FilterPanel = () => (
    <div className="space-y-6">
      {/* Search */}
      <div>
        <label className="text-sm font-medium mb-2 block">Search Tenders</label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search by title, description, or issuer..."
            value={filters.searchQuery}
            onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
            className="pl-10"
          />
        </div>
      </div>

      {/* Categories */}
      <div>
        <label className="text-sm font-medium mb-2 block">Categories</label>
        <div className="space-y-2">
          {TENDER_CATEGORIES.map(category => (
            <div key={category} className="flex items-center space-x-2">
              <Checkbox
                id={category}
                checked={filters.categories.includes(category)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setFilters(prev => ({
                      ...prev,
                      categories: [...prev.categories, category]
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      categories: prev.categories.filter(c => c !== category)
                    }));
                  }
                }}
              />
              <label htmlFor={category} className="text-sm capitalize">
                {category.replace('_', ' ')}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Statuses */}
      <div>
        <label className="text-sm font-medium mb-2 block">Status</label>
        <div className="space-y-2">
          {TENDER_STATUSES.map(status => (
            <div key={status} className="flex items-center space-x-2">
              <Checkbox
                id={status}
                checked={filters.statuses.includes(status)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setFilters(prev => ({
                      ...prev,
                      statuses: [...prev.statuses, status]
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      statuses: prev.statuses.filter(s => s !== status)
                    }));
                  }
                }}
              />
              <label htmlFor={status} className="text-sm capitalize">
                {status}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Value Range */}
      <div>
        <label className="text-sm font-medium mb-2 block">
          Value Range: {formatCurrency(filters.valueRange[0])} - {formatCurrency(filters.valueRange[1])}
        </label>
        <Slider
          value={filters.valueRange}
          onValueChange={(value) => setFilters(prev => ({ ...prev, valueRange: value as [number, number] }))}
          max={100000000}
          step={100000}
          className="w-full"
        />
      </div>

      {/* Date Range */}
      <div>
        <label className="text-sm font-medium mb-2 block">Closing Date Range</label>
        <div className="grid grid-cols-2 gap-2">
          <Input
            type="date"
            value={filters.dateRange.from}
            onChange={(e) => setFilters(prev => ({
              ...prev,
              dateRange: { ...prev.dateRange, from: e.target.value }
            }))}
          />
          <Input
            type="date"
            value={filters.dateRange.to}
            onChange={(e) => setFilters(prev => ({
              ...prev,
              dateRange: { ...prev.dateRange, to: e.target.value }
            }))}
          />
        </div>
      </div>

      {/* Location */}
      <div>
        <label className="text-sm font-medium mb-2 block">Location</label>
        <Input
          placeholder="Filter by location..."
          value={filters.location}
          onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
        />
      </div>

      {/* Clear Filters */}
      <Button
        variant="outline"
        onClick={() => setFilters({
          searchQuery: '',
          categories: [],
          statuses: ['open', 'published'],
          valueRange: [0, 100000000],
          dateRange: { from: '', to: '' },
          location: ''
        })}
        className="w-full"
      >
        Clear All Filters
      </Button>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tender Discovery</h1>
            <p className="text-gray-600">Find and track government and private sector tenders</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={loadStats}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Sheet open={showFilters} onOpenChange={setShowFilters}>
              <SheetTrigger asChild>
                <Button variant="outline">
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                  {(filters.categories.length > 0 || filters.searchQuery || filters.location) && (
                    <Badge variant="secondary" className="ml-2">
                      {filters.categories.length + (filters.searchQuery ? 1 : 0) + (filters.location ? 1 : 0)}
                    </Badge>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filter Tenders</SheetTitle>
                  <SheetDescription>
                    Refine your tender search with advanced filters
                  </SheetDescription>
                </SheetHeader>
                <div className="mt-6">
                  <FilterPanel />
                </div>
              </SheetContent>
            </Sheet>
            {selectedTenders.length > 0 && (
              <Button onClick={handleExportSelected}>
                <Download className="w-4 h-4 mr-2" />
                Export Selected ({selectedTenders.length})
              </Button>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Tenders</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Search className="w-4 h-4 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Open Tenders</p>
                  <p className="text-2xl font-bold text-green-600">{stats.open}</p>
                </div>
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Target className="w-4 h-4 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Selected</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.selected}</p>
                </div>
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <BookmarkPlus className="w-4 h-4 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Value</p>
                  <p className="text-lg font-bold">{formatCurrency(stats.totalValue)}</p>
                </div>
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 font-bold">R</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6">
        <Tabs value={activeView} onValueChange={(value) => setActiveView(value as 'map' | 'list')}>
          <TabsList className="mb-4">
            <TabsTrigger value="map" className="flex items-center space-x-2">
              <Map className="w-4 h-4" />
              <span>Map View</span>
            </TabsTrigger>
            <TabsTrigger value="list" className="flex items-center space-x-2">
              <List className="w-4 h-4" />
              <span>List View</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="map" className="h-[calc(100vh-300px)]">
            <TenderDiscoveryMap
              onTenderSelect={handleTenderSelect}
              selectedTenders={selectedTenders}
              onTenderToggle={handleTenderToggle}
            />
          </TabsContent>

          <TabsContent value="list" className="h-[calc(100vh-300px)] overflow-auto">
            <TenderListView
              onTenderSelect={handleTenderSelect}
              selectedTenders={selectedTenders}
              onTenderToggle={handleTenderToggle}
              searchQuery={filters.searchQuery}
              filters={filters}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Tender Details Sheet */}
      <Sheet open={showTenderDetails} onOpenChange={setShowTenderDetails}>
        <SheetContent className="w-[600px] sm:w-[600px]">
          {selectedTender && (
            <>
              <SheetHeader>
                <SheetTitle>{selectedTender.title}</SheetTitle>
                <SheetDescription>{selectedTender.issuer}</SheetDescription>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Estimated Value</p>
                    <p className="font-semibold">{formatCurrency(selectedTender.estimated_value || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Closing Date</p>
                    <p className="font-semibold">
                      {selectedTender.closing_date ? new Date(selectedTender.closing_date).toLocaleDateString() : 'TBD'}
                    </p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-gray-600 mb-2">Description</p>
                  <p className="text-sm">{selectedTender.description}</p>
                </div>

                <div className="flex space-x-3">
                  <Button 
                    className="flex-1"
                    onClick={() => handleStartBid(selectedTender)}
                  >
                    <Target className="w-4 h-4 mr-2" />
                    Start Bid
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => handleTenderToggle(selectedTender.id)}
                  >
                    {selectedTenders.includes(selectedTender.id) ? 'Remove' : 'Select'}
                  </Button>
                </div>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default TenderDiscoveryPage;
