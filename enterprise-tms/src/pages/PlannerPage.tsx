import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  Clock, 
  Users, 
  CheckSquare, 
  AlertTriangle,
  Plus,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  MapPin,
  Building,
  Target,
  User,
  Timer
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { tenderMeetingService, tenderTaskService } from '../services/api';
import type { TenderMeeting, TenderTask } from '../lib/supabase';
import TimelineView from '../components/planner/TimelineView';

interface PlannerStats {
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  upcomingMeetings: number;
  activeTenders: number;
}

const PlannerPage: React.FC = () => {
  const [meetings, setMeetings] = useState<TenderMeeting[]>([]);
  const [tasks, setTasks] = useState<TenderTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [viewMode, setViewMode] = useState<'calendar' | 'timeline' | 'kanban'>('calendar');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterAssignee, setFilterAssignee] = useState<string>('all');
  const [stats, setStats] = useState<PlannerStats>({
    totalTasks: 0,
    completedTasks: 0,
    overdueTasks: 0,
    upcomingMeetings: 0,
    activeTenders: 0
  });

  useEffect(() => {
    loadPlannerData();
  }, []);

  const loadPlannerData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [meetingsData, tasksData] = await Promise.all([
        tenderMeetingService.getUpcomingMeetings(),
        tenderTaskService.getUserTasks('current-user-id') // This would be dynamic
      ]);

      setMeetings(meetingsData);
      setTasks(tasksData);
      
      // Calculate stats
      const overdueTasks = tasksData.filter(task => 
        new Date(task.due_date) < new Date() && task.status !== 'completed'
      ).length;
      
      const completedTasks = tasksData.filter(task => task.status === 'completed').length;
      
      setStats({
        totalTasks: tasksData.length,
        completedTasks,
        overdueTasks,
        upcomingMeetings: meetingsData.length,
        activeTenders: new Set(tasksData.map(t => t.tender_id)).size
      });
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load planner data');
    } finally {
      setLoading(false);
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      default:
        return 'bg-green-500';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-ZA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDaysUntilDue = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const filteredTasks = tasks.filter(task => {
    if (filterStatus !== 'all' && task.status !== filterStatus) return false;
    if (filterAssignee !== 'all' && task.assigned_to !== filterAssignee) return false;
    return true;
  });

  const upcomingTasks = filteredTasks
    .filter(task => task.status !== 'completed')
    .sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime())
    .slice(0, 10);

  const todaysTasks = filteredTasks.filter(task => {
    const taskDate = new Date(task.due_date).toISOString().split('T')[0];
    return taskDate === selectedDate;
  });

  const todaysMeetings = meetings.filter(meeting => {
    const meetingDate = new Date(meeting.meeting_date).toISOString().split('T')[0];
    return meetingDate === selectedDate;
  });

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading planner...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadPlannerData}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tender Planner</h1>
            <p className="text-gray-600">Manage tasks, meetings, and deadlines across all your tenders</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={loadPlannerData}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Tasks</p>
                  <p className="text-2xl font-bold">{stats.totalTasks}</p>
                </div>
                <CheckSquare className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-green-600">{stats.completedTasks}</p>
                </div>
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckSquare className="w-4 h-4 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Overdue</p>
                  <p className="text-2xl font-bold text-red-600">{stats.overdueTasks}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Meetings</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.upcomingMeetings}</p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Tenders</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.activeTenders}</p>
                </div>
                <Target className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          {/* Left Column - Today's Schedule */}
          <div className="lg:col-span-1 space-y-6">
            {/* Date Selector */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Today's Schedule
                </CardTitle>
                <Input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="mt-2"
                />
              </CardHeader>
              <CardContent>
                {/* Today's Meetings */}
                <div className="mb-4">
                  <h4 className="font-medium text-sm text-gray-700 mb-2">Meetings ({todaysMeetings.length})</h4>
                  {todaysMeetings.length === 0 ? (
                    <p className="text-sm text-gray-500">No meetings scheduled</p>
                  ) : (
                    <div className="space-y-2">
                      {todaysMeetings.map(meeting => (
                        <div key={meeting.id} className="flex items-center space-x-3 p-2 bg-blue-50 rounded-lg">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{meeting.title}</p>
                            <p className="text-xs text-gray-600">
                              {meeting.meeting_time && formatTime(meeting.meeting_date + 'T' + meeting.meeting_time)}
                              {meeting.location && ` • ${meeting.location}`}
                            </p>
                          </div>
                          {meeting.is_compulsory && (
                            <Badge variant="destructive" className="text-xs">Required</Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Today's Tasks */}
                <div>
                  <h4 className="font-medium text-sm text-gray-700 mb-2">Tasks ({todaysTasks.length})</h4>
                  {todaysTasks.length === 0 ? (
                    <p className="text-sm text-gray-500">No tasks due today</p>
                  ) : (
                    <div className="space-y-2">
                      {todaysTasks.map(task => (
                        <div key={task.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                          <Checkbox
                            checked={task.status === 'completed'}
                            onCheckedChange={() => {
                              // Handle task completion
                            }}
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{task.title}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge className={getTaskStatusColor(task.status)} variant="secondary">
                                {task.status}
                              </Badge>
                              <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`}></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Progress Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Task Completion</span>
                      <span>{Math.round((stats.completedTasks / stats.totalTasks) * 100)}%</span>
                    </div>
                    <Progress value={(stats.completedTasks / stats.totalTasks) * 100} />
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">{stats.completedTasks}</p>
                      <p className="text-xs text-green-700">Completed</p>
                    </div>
                    <div className="p-3 bg-red-50 rounded-lg">
                      <p className="text-2xl font-bold text-red-600">{stats.overdueTasks}</p>
                      <p className="text-xs text-red-700">Overdue</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Task Management */}
          <div className="lg:col-span-2">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Task Management</CardTitle>
                  <div className="flex items-center space-x-2">
                    <Select value={filterStatus} onValueChange={setFilterStatus}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="sm">
                      <Filter className="w-4 h-4 mr-1" />
                      Filter
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="h-[calc(100%-80px)] overflow-auto">
                <div className="space-y-3">
                  {upcomingTasks.map((task, index) => {
                    const daysUntilDue = getDaysUntilDue(task.due_date);
                    const isOverdue = daysUntilDue < 0;
                    const isUrgent = daysUntilDue <= 3 && daysUntilDue >= 0;
                    
                    return (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`p-4 border rounded-lg hover:shadow-md transition-shadow ${
                          isOverdue ? 'border-red-200 bg-red-50' : 
                          isUrgent ? 'border-orange-200 bg-orange-50' : 
                          'border-gray-200 bg-white'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <Checkbox
                              checked={task.status === 'completed'}
                              onCheckedChange={() => {
                                // Handle task completion
                              }}
                            />
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{task.title}</h4>
                              <p className="text-xs text-gray-600 mt-1">{task.description}</p>
                              <div className="flex items-center space-x-4 mt-2">
                                <div className="flex items-center space-x-1">
                                  <Calendar className="w-3 h-3 text-gray-400" />
                                  <span className={`text-xs ${
                                    isOverdue ? 'text-red-600 font-medium' : 
                                    isUrgent ? 'text-orange-600 font-medium' : 
                                    'text-gray-600'
                                  }`}>
                                    {formatDate(task.due_date)}
                                    {isOverdue && ' (Overdue)'}
                                    {isUrgent && !isOverdue && ` (${daysUntilDue} day${daysUntilDue !== 1 ? 's' : ''})`}
                                  </span>
                                </div>
                                {task.assigned_to_role && (
                                  <div className="flex items-center space-x-1">
                                    <User className="w-3 h-3 text-gray-400" />
                                    <span className="text-xs text-gray-600 capitalize">
                                      {task.assigned_to_role.replace('_', ' ')}
                                    </span>
                                  </div>
                                )}
                                {task.estimated_hours && (
                                  <div className="flex items-center space-x-1">
                                    <Timer className="w-3 h-3 text-gray-400" />
                                    <span className="text-xs text-gray-600">
                                      {task.estimated_hours}h
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`}></div>
                            <Badge className={getTaskStatusColor(task.status)} variant="secondary">
                              {task.status.replace('_', ' ')}
                            </Badge>
                            <Button variant="ghost" size="sm">
                              <Eye className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                        
                        {/* Progress bar for checklist items */}
                        {task.checklist_items && task.checklist_items.length > 0 && (
                          <div className="mt-3">
                            <div className="flex justify-between text-xs text-gray-600 mb-1">
                              <span>Checklist Progress</span>
                              <span>
                                {task.checklist_items.filter(item => item.is_completed).length} / {task.checklist_items.length}
                              </span>
                            </div>
                            <Progress 
                              value={(task.checklist_items.filter(item => item.is_completed).length / task.checklist_items.length) * 100}
                              className="h-1"
                            />
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
                
                {upcomingTasks.length === 0 && (
                  <div className="text-center py-12">
                    <CheckSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
                    <p className="text-gray-600">All caught up! No tasks match your current filters.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlannerPage;
