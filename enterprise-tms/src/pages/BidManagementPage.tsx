import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Target, 
  Plus, 
  Filter, 
  Search, 
  Download,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Clock,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  FileText,
  Users,
  Calendar
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { tenderBidService, tenderService } from '../services/api';
import type { TenderBid, TenderData } from '../lib/supabase';

interface BidStats {
  totalBids: number;
  draftBids: number;
  submittedBids: number;
  awardedBids: number;
  totalValue: number;
  winRate: number;
}

const BidManagementPage: React.FC = () => {
  const [bids, setBids] = useState<TenderBid[]>([]);
  const [selectedTenders, setSelectedTenders] = useState<TenderData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedBid, setSelectedBid] = useState<TenderBid | null>(null);
  const [showBidDetails, setShowBidDetails] = useState(false);
  const [stats, setStats] = useState<BidStats>({
    totalBids: 0,
    draftBids: 0,
    submittedBids: 0,
    awardedBids: 0,
    totalValue: 0,
    winRate: 0
  });

  useEffect(() => {
    loadBidData();
    loadSelectedTenders();
  }, []);

  const loadBidData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const bidsData = await tenderBidService.getUserBids('current-user-id'); // This would be dynamic
      setBids(bidsData);
      
      // Calculate stats
      const totalValue = bidsData.reduce((sum, bid) => sum + bid.bid_amount, 0);
      const awardedBids = bidsData.filter(bid => bid.status === 'awarded').length;
      const submittedBids = bidsData.filter(bid => bid.status === 'submitted' || bid.status === 'awarded' || bid.status === 'rejected').length;
      const winRate = submittedBids > 0 ? (awardedBids / submittedBids) * 100 : 0;
      
      setStats({
        totalBids: bidsData.length,
        draftBids: bidsData.filter(bid => bid.status === 'draft').length,
        submittedBids: bidsData.filter(bid => bid.status === 'submitted').length,
        awardedBids,
        totalValue,
        winRate
      });
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load bid data');
    } finally {
      setLoading(false);
    }
  };

  const loadSelectedTenders = () => {
    // Load selected tenders from localStorage or state management
    const saved = localStorage.getItem('selectedTenders');
    if (saved) {
      try {
        const tenderIds = JSON.parse(saved);
        // In a real app, you'd fetch the full tender data
        // For now, we'll just store the IDs
        console.log('Selected tender IDs:', tenderIds);
      } catch (error) {
        console.error('Failed to load selected tenders:', error);
      }
    }
  };

  const handleCreateBid = async (tender: TenderData) => {
    try {
      const newBid = await tenderBidService.createBid({
        tender_id: tender.id,
        bidder_id: 'current-user-id', // This would be dynamic
        bid_amount: 0,
        currency: 'ZAR',
        status: 'draft'
      });
      
      setBids(prev => [newBid, ...prev]);
      setSelectedBid(newBid);
      setShowBidDetails(true);
    } catch (error) {
      console.error('Failed to create bid:', error);
    }
  };

  const handleSubmitBid = async (bidId: string) => {
    try {
      const updatedBid = await tenderBidService.submitBid(bidId);
      setBids(prev => prev.map(bid => bid.id === bidId ? updatedBid : bid));
    } catch (error) {
      console.error('Failed to submit bid:', error);
    }
  };

  const getBidStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'shortlisted':
        return 'bg-yellow-100 text-yellow-800';
      case 'awarded':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBidStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="w-4 h-4" />;
      case 'submitted':
        return <Clock className="w-4 h-4" />;
      case 'shortlisted':
        return <TrendingUp className="w-4 h-4" />;
      case 'awarded':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
        return <XCircle className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const filteredBids = bids.filter(bid => {
    if (statusFilter !== 'all' && bid.status !== statusFilter) return false;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        bid.tenders?.title?.toLowerCase().includes(query) ||
        bid.tenders?.issuer?.toLowerCase().includes(query)
      );
    }
    return true;
  });

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading bid management...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadBidData}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Bid Management</h1>
            <p className="text-gray-600">Track and manage all your tender bids</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={loadBidData}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Bid
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Bids</p>
                  <p className="text-2xl font-bold">{stats.totalBids}</p>
                </div>
                <Target className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Draft</p>
                  <p className="text-2xl font-bold text-gray-600">{stats.draftBids}</p>
                </div>
                <Edit className="w-8 h-8 text-gray-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Submitted</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.submittedBids}</p>
                </div>
                <Clock className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Awarded</p>
                  <p className="text-2xl font-bold text-green-600">{stats.awardedBids}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Win Rate</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.winRate.toFixed(1)}%</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Value</p>
                  <p className="text-lg font-bold">{formatCurrency(stats.totalValue)}</p>
                </div>
                <DollarSign className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6">
        <Tabs defaultValue="active-bids" className="h-full">
          <div className="flex items-center justify-between mb-4">
            <TabsList>
              <TabsTrigger value="active-bids">Active Bids</TabsTrigger>
              <TabsTrigger value="selected-tenders">Selected Tenders</TabsTrigger>
              <TabsTrigger value="bid-history">Bid History</TabsTrigger>
            </TabsList>
            
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search bids..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="shortlisted">Shortlisted</SelectItem>
                  <SelectItem value="awarded">Awarded</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <TabsContent value="active-bids" className="h-[calc(100%-60px)] overflow-auto">
            <div className="space-y-4">
              {filteredBids.map((bid, index) => (
                <motion.div
                  key={bid.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center space-x-2">
                            {getBidStatusIcon(bid.status)}
                            <span>{bid.tenders?.title || 'Untitled Tender'}</span>
                          </CardTitle>
                          <CardDescription className="mt-1">
                            {bid.tenders?.issuer} • Bid Amount: {formatCurrency(bid.bid_amount)}
                          </CardDescription>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getBidStatusColor(bid.status)}>
                            {bid.status}
                          </Badge>
                          {bid.ranking && (
                            <Badge variant="outline">
                              Rank #{bid.ranking}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-600">Submission Date</p>
                          <p className="font-medium">
                            {bid.submission_date ? formatDate(bid.submission_date) : 'Not submitted'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Technical Score</p>
                          <p className="font-medium">
                            {bid.technical_score ? `${bid.technical_score}/100` : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Commercial Score</p>
                          <p className="font-medium">
                            {bid.commercial_score ? `${bid.commercial_score}/100` : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Total Score</p>
                          <p className="font-medium">
                            {bid.total_score ? `${bid.total_score}/100` : 'N/A'}
                          </p>
                        </div>
                      </div>

                      {/* Progress indicators */}
                      {bid.status === 'draft' && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm mb-1">
                            <span>Completion Progress</span>
                            <span>65%</span>
                          </div>
                          <Progress value={65} />
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {bid.tenders?.closing_date && (
                            <div className="flex items-center space-x-1 text-sm text-gray-600">
                              <Calendar className="w-4 h-4" />
                              <span>Closes: {formatDate(bid.tenders.closing_date)}</span>
                            </div>
                          )}
                          {bid.team_members && bid.team_members.length > 0 && (
                            <div className="flex items-center space-x-1 text-sm text-gray-600">
                              <Users className="w-4 h-4" />
                              <span>{bid.team_members.length} team members</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedBid(bid);
                              setShowBidDetails(true);
                            }}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                          {bid.status === 'draft' && (
                            <Button
                              size="sm"
                              onClick={() => handleSubmitBid(bid.id)}
                            >
                              <Target className="w-4 h-4 mr-1" />
                              Submit Bid
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {filteredBids.length === 0 && (
              <div className="text-center py-12">
                <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No bids found</h3>
                <p className="text-gray-600">
                  {searchQuery || statusFilter !== 'all' 
                    ? 'No bids match your current filters'
                    : 'Start by selecting tenders from the discovery page'
                  }
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="selected-tenders" className="h-[calc(100%-60px)] overflow-auto">
            <div className="text-center py-12">
              <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Selected Tenders</h3>
              <p className="text-gray-600 mb-4">
                Tenders you've selected from the discovery page will appear here
              </p>
              <Button>
                Go to Tender Discovery
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="bid-history" className="h-[calc(100%-60px)] overflow-auto">
            <div className="space-y-4">
              {bids.filter(bid => bid.status === 'awarded' || bid.status === 'rejected').map((bid, index) => (
                <Card key={bid.id} className="opacity-75">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{bid.tenders?.title}</h4>
                        <p className="text-sm text-gray-600">{bid.tenders?.issuer}</p>
                      </div>
                      <div className="text-right">
                        <Badge className={getBidStatusColor(bid.status)}>
                          {bid.status}
                        </Badge>
                        <p className="text-sm text-gray-600 mt-1">
                          {bid.submission_date && formatDate(bid.submission_date)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Bid Details Sheet */}
      <Sheet open={showBidDetails} onOpenChange={setShowBidDetails}>
        <SheetContent className="w-[600px] sm:w-[600px]">
          {selectedBid && (
            <>
              <SheetHeader>
                <SheetTitle>{selectedBid.tenders?.title}</SheetTitle>
                <SheetDescription>{selectedBid.tenders?.issuer}</SheetDescription>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Bid Amount</p>
                    <p className="font-semibold">{formatCurrency(selectedBid.bid_amount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <Badge className={getBidStatusColor(selectedBid.status)}>
                      {selectedBid.status}
                    </Badge>
                  </div>
                </div>
                
                {selectedBid.ai_analysis && (
                  <div>
                    <h4 className="font-medium mb-2">AI Analysis</h4>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm">
                        Match Score: {selectedBid.ai_analysis.match_score}% • 
                        Win Probability: {selectedBid.ai_analysis.win_probability}%
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex space-x-3">
                  <Button className="flex-1">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Bid
                  </Button>
                  {selectedBid.status === 'draft' && (
                    <Button 
                      variant="outline"
                      onClick={() => handleSubmitBid(selectedBid.id)}
                    >
                      Submit
                    </Button>
                  )}
                </div>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default BidManagementPage;
