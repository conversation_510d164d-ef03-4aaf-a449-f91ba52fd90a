import { supabase } from '../lib/supabase'
import type { 
  BeeProfile, 
  TransportBooking, 
  BeeCourierAssignment, 
  BeeTask, 
  TenderData 
} from '../lib/supabase'

// Bee Management Services
export const beeService = {
  // Get all bee profiles
  async getAllBees(): Promise<BeeProfile[]> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get available bees
  async getAvailableBees(): Promise<BeeProfile[]> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .select('*')
      .eq('is_active', true)
      .eq('availability_status', 'available')
      .order('rating', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get bee by ID
  async getBeeById(id: number): Promise<BeeProfile | null> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Update bee profile
  async updateBee(id: number, updates: Partial<BeeProfile>): Promise<BeeProfile> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get bee performance metrics
  async getBeePerformance(beeId: string): Promise<any> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select(`
        assignment_status,
        on_time_pickup,
        on_time_delivery,
        customer_satisfaction_score,
        final_amount,
        completed_at
      `)
      .eq('bee_user_id', beeId)
      .not('completed_at', 'is', null)
    
    if (error) throw error
    return data || []
  }
}

// Transport Booking Services
export const transportService = {
  // Get all transport bookings
  async getAllBookings(): Promise<TransportBooking[]> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new booking
  async createBooking(booking: Partial<TransportBooking>): Promise<TransportBooking> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .insert(booking)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update booking status
  async updateBookingStatus(id: number, status: string): Promise<TransportBooking> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .update({ 
        status, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get bookings by status
  async getBookingsByStatus(status: string): Promise<TransportBooking[]> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get booking analytics
  async getBookingAnalytics(): Promise<any> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .select(`
        status,
        estimated_fare,
        actual_fare,
        distance_km,
        duration_minutes,
        rating,
        created_at
      `)
    
    if (error) throw error
    return data || []
  }
}

// Courier Assignment Services
export const courierService = {
  // Get all courier assignments
  async getAllAssignments(): Promise<BeeCourierAssignment[]> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new assignment
  async createAssignment(assignment: Partial<BeeCourierAssignment>): Promise<BeeCourierAssignment> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .insert({
        ...assignment,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update assignment status
  async updateAssignmentStatus(
    id: string, 
    status: string, 
    additionalData?: Partial<BeeCourierAssignment>
  ): Promise<BeeCourierAssignment> {
    const updateData = {
      assignment_status: status,
      updated_at: new Date().toISOString(),
      ...additionalData
    }

    // Set timestamp based on status
    if (status === 'accepted') updateData.accepted_at = new Date().toISOString()
    if (status === 'started') updateData.started_at = new Date().toISOString()
    if (status === 'completed') updateData.completed_at = new Date().toISOString()
    if (status === 'cancelled') updateData.cancelled_at = new Date().toISOString()

    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get assignments by bee
  async getAssignmentsByBee(beeUserId: string): Promise<BeeCourierAssignment[]> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select('*')
      .eq('bee_user_id', beeUserId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get active assignments
  async getActiveAssignments(): Promise<BeeCourierAssignment[]> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select('*')
      .in('assignment_status', ['assigned', 'accepted', 'started'])
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  }
}

// Task Management Services
export const taskService = {
  // Get all tasks
  async getAllTasks(): Promise<BeeTask[]> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new task
  async createTask(task: Partial<BeeTask>): Promise<BeeTask> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .insert({
        ...task,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update task status
  async updateTaskStatus(id: string, status: string): Promise<BeeTask> {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    }

    // Set timestamp based on status
    if (status === 'assigned') updateData.assigned_at = new Date().toISOString()
    if (status === 'started') updateData.started_at = new Date().toISOString()
    if (status === 'completed') updateData.completed_at = new Date().toISOString()

    const { data, error } = await supabase
      .from('bee_tasks')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get tasks by status
  async getTasksByStatus(status: string): Promise<BeeTask[]> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get urgent tasks
  async getUrgentTasks(): Promise<BeeTask[]> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .select('*')
      .eq('is_urgent', true)
      .in('status', ['pending', 'assigned'])
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  }
}

// Analytics Services
export const analyticsService = {
  // Get dashboard metrics
  async getDashboardMetrics(): Promise<any> {
    const [bees, bookings, assignments, tasks] = await Promise.all([
      supabase.from('bee_profiles').select('id, is_active, availability_status'),
      supabase.from('transport_bookings').select('id, status, estimated_fare, actual_fare'),
      supabase.from('bee_courier_assignments').select('id, assignment_status, final_amount'),
      supabase.from('bee_tasks').select('id, status, payment_amount')
    ])

    return {
      bees: bees.data || [],
      bookings: bookings.data || [],
      assignments: assignments.data || [],
      tasks: tasks.data || []
    }
  },

  // Get performance analytics
  async getPerformanceAnalytics(): Promise<any> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select(`
        assignment_status,
        on_time_pickup,
        on_time_delivery,
        customer_satisfaction_score,
        final_amount,
        actual_duration_minutes,
        estimated_duration_minutes,
        created_at,
        completed_at
      `)
      .not('completed_at', 'is', null)
    
    if (error) throw error
    return data || []
  }
}
