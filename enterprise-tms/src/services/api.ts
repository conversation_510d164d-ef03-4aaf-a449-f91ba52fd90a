import { supabase } from '../lib/supabase'
import type {
  BeeProfile,
  TransportBooking,
  BeeCourierAssignment,
  BeeTask,
  TenderData,
  TenderMeeting,
  TenderTask,
  TenderBid,
  TenderDocument,
  TenderComplianceItem,
  TenderBidAIAnalysis
} from '../lib/supabase'

// Bee Management Services
export const beeService = {
  // Get all bee profiles
  async getAllBees(): Promise<BeeProfile[]> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get available bees
  async getAvailableBees(): Promise<BeeProfile[]> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .select('*')
      .eq('is_active', true)
      .eq('availability_status', 'available')
      .order('rating', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get bee by ID
  async getBeeById(id: number): Promise<BeeProfile | null> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Update bee profile
  async updateBee(id: number, updates: Partial<BeeProfile>): Promise<BeeProfile> {
    const { data, error } = await supabase
      .from('bee_profiles')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get bee performance metrics
  async getBeePerformance(beeId: string): Promise<any> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select(`
        assignment_status,
        on_time_pickup,
        on_time_delivery,
        customer_satisfaction_score,
        final_amount,
        completed_at
      `)
      .eq('bee_user_id', beeId)
      .not('completed_at', 'is', null)
    
    if (error) throw error
    return data || []
  }
}

// Transport Booking Services
export const transportService = {
  // Get all transport bookings
  async getAllBookings(): Promise<TransportBooking[]> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new booking
  async createBooking(booking: Partial<TransportBooking>): Promise<TransportBooking> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .insert(booking)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update booking status
  async updateBookingStatus(id: number, status: string): Promise<TransportBooking> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .update({ 
        status, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get bookings by status
  async getBookingsByStatus(status: string): Promise<TransportBooking[]> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get booking analytics
  async getBookingAnalytics(): Promise<any> {
    const { data, error } = await supabase
      .from('transport_bookings')
      .select(`
        status,
        estimated_fare,
        actual_fare,
        distance_km,
        duration_minutes,
        rating,
        created_at
      `)
    
    if (error) throw error
    return data || []
  }
}

// Courier Assignment Services
export const courierService = {
  // Get all courier assignments
  async getAllAssignments(): Promise<BeeCourierAssignment[]> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new assignment
  async createAssignment(assignment: Partial<BeeCourierAssignment>): Promise<BeeCourierAssignment> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .insert({
        ...assignment,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update assignment status
  async updateAssignmentStatus(
    id: string, 
    status: string, 
    additionalData?: Partial<BeeCourierAssignment>
  ): Promise<BeeCourierAssignment> {
    const updateData = {
      assignment_status: status,
      updated_at: new Date().toISOString(),
      ...additionalData
    }

    // Set timestamp based on status
    if (status === 'accepted') updateData.accepted_at = new Date().toISOString()
    if (status === 'started') updateData.started_at = new Date().toISOString()
    if (status === 'completed') updateData.completed_at = new Date().toISOString()
    if (status === 'cancelled') updateData.cancelled_at = new Date().toISOString()

    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get assignments by bee
  async getAssignmentsByBee(beeUserId: string): Promise<BeeCourierAssignment[]> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select('*')
      .eq('bee_user_id', beeUserId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get active assignments
  async getActiveAssignments(): Promise<BeeCourierAssignment[]> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select('*')
      .in('assignment_status', ['assigned', 'accepted', 'started'])
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  }
}

// Task Management Services
export const taskService = {
  // Get all tasks
  async getAllTasks(): Promise<BeeTask[]> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new task
  async createTask(task: Partial<BeeTask>): Promise<BeeTask> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .insert({
        ...task,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update task status
  async updateTaskStatus(id: string, status: string): Promise<BeeTask> {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    }

    // Set timestamp based on status
    if (status === 'assigned') updateData.assigned_at = new Date().toISOString()
    if (status === 'started') updateData.started_at = new Date().toISOString()
    if (status === 'completed') updateData.completed_at = new Date().toISOString()

    const { data, error } = await supabase
      .from('bee_tasks')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get tasks by status
  async getTasksByStatus(status: string): Promise<BeeTask[]> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get urgent tasks
  async getUrgentTasks(): Promise<BeeTask[]> {
    const { data, error } = await supabase
      .from('bee_tasks')
      .select('*')
      .eq('is_urgent', true)
      .in('status', ['pending', 'assigned'])
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  }
}

// Analytics Services
export const analyticsService = {
  // Get dashboard metrics
  async getDashboardMetrics(): Promise<any> {
    const [bees, bookings, assignments, tasks] = await Promise.all([
      supabase.from('bee_profiles').select('id, is_active, availability_status'),
      supabase.from('transport_bookings').select('id, status, estimated_fare, actual_fare'),
      supabase.from('bee_courier_assignments').select('id, assignment_status, final_amount'),
      supabase.from('bee_tasks').select('id, status, payment_amount')
    ])

    return {
      bees: bees.data || [],
      bookings: bookings.data || [],
      assignments: assignments.data || [],
      tasks: tasks.data || []
    }
  },

  // Get performance analytics
  async getPerformanceAnalytics(): Promise<any> {
    const { data, error } = await supabase
      .from('bee_courier_assignments')
      .select(`
        assignment_status,
        on_time_pickup,
        on_time_delivery,
        customer_satisfaction_score,
        final_amount,
        actual_duration_minutes,
        estimated_duration_minutes,
        created_at,
        completed_at
      `)
      .not('completed_at', 'is', null)

    if (error) throw error
    return data || []
  }
}

// Province coordinates mapping for South Africa
const provinceCoordinates: { [key: string]: [number, number] } = {
  'Gauteng': [-26.2041, 28.0473],
  'Western Cape': [-33.9249, 18.4241],
  'KwaZulu-Natal': [-29.8587, 31.0218],
  'Eastern Cape': [-32.2968, 26.4194],
  'Free State': [-29.1178, 26.2336],
  'Limpopo': [-23.4013, 29.4179],
  'Mpumalanga': [-25.5653, 30.5279],
  'Northern Cape': [-28.7282, 24.7499],
  'North West': [-25.8601, 25.6402],
}

// Helper function to transform database records to TenderData format
const transformTenderData = (record: any): TenderData => {
  const province = record.province || 'Unknown'
  const coordinates = provinceCoordinates[province] || [-29.0852, 24.6727] // Default to center of SA

  return {
    ...record,
    // Add backward compatibility mappings
    issuer: record.issuer_name,
    estimated_value: record.tender_value,
    category: record.category_code,
    location: [record.province, record.city].filter(Boolean).join(', ') || 'Unknown',
    status: record.should_include_in_main ? 'open' : 'closed',
    ai_match_score: record.success_prediction,
    risk_level: record.risk_score ? (
      record.risk_score < 30 ? 'low' :
      record.risk_score < 70 ? 'medium' : 'high'
    ) : 'medium',
    // Add coordinates based on province
    latitude: record.latitude || coordinates[0],
    longitude: record.longitude || coordinates[1],
  }
}

// Tender Management Services
export const tenderService = {
  // Get all tenders with optional filtering
  async getAllTenders(filters?: {
    category?: string
    status?: string
    location?: string
    minValue?: number
    maxValue?: number
    dateFrom?: string
    dateTo?: string
  }): Promise<TenderData[]> {
    let query = supabase
      .from('tenders')
      .select('*')
      .eq('should_include_in_main', true)
      .order('created_at', { ascending: false })

    if (filters) {
      if (filters.category) query = query.eq('category_code', filters.category)
      if (filters.location) query = query.or(`province.ilike.%${filters.location}%,city.ilike.%${filters.location}%`)
      if (filters.minValue) query = query.gte('tender_value', filters.minValue)
      if (filters.maxValue) query = query.lte('tender_value', filters.maxValue)
      if (filters.dateFrom) query = query.gte('closing_date', filters.dateFrom)
      if (filters.dateTo) query = query.lte('closing_date', filters.dateTo)
    }

    const { data, error } = await query
    if (error) throw error
    return (data || []).map(transformTenderData)
  },

  // Get tender by ID with full details
  async getTenderById(id: string): Promise<TenderData | null> {
    const { data, error } = await supabase
      .from('tenders')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data ? transformTenderData(data) : null
  },

  // Search tenders with text search
  async searchTenders(searchQuery: string, filters?: any): Promise<TenderData[]> {
    let query = supabase
      .from('tenders')
      .select('*')
      .or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,issuer_name.ilike.%${searchQuery}%`)
      .eq('should_include_in_main', true)
      .order('success_prediction', { ascending: false })

    if (filters) {
      if (filters.category) query = query.eq('category_code', filters.category)
      if (filters.location) query = query.or(`province.ilike.%${filters.location}%,city.ilike.%${filters.location}%`)
    }

    const { data, error } = await query
    if (error) throw error
    return (data || []).map(transformTenderData)
  },

  // Get tenders by location (for map view)
  async getTendersByLocation(bounds?: {
    north: number
    south: number
    east: number
    west: number
  }): Promise<TenderData[]> {
    let query = supabase
      .from('tenders')
      .select('*')
      .eq('should_include_in_main', true)
      .not('province', 'is', null)
      .order('created_at', { ascending: false })
      .limit(100) // Limit for performance

    const { data, error } = await query
    if (error) throw error

    // Transform data and add coordinates based on province
    const transformedData = (data || []).map(transformTenderData)

    // Filter by bounds if provided
    if (bounds) {
      return transformedData.filter(tender =>
        tender.latitude && tender.longitude &&
        tender.latitude >= bounds.south &&
        tender.latitude <= bounds.north &&
        tender.longitude >= bounds.west &&
        tender.longitude <= bounds.east
      )
    }

    return transformedData
  },

  // Get tender documents
  async getTenderDocuments(tenderId: string): Promise<TenderDocument[]> {
    const { data, error } = await supabase
      .from('tender_documents')
      .select('*')
      .eq('tender_id', tenderId)
      .order('uploaded_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  // Create new tender (for admin/scraper)
  async createTender(tender: Partial<TenderData>): Promise<TenderData> {
    const { data, error } = await supabase
      .from('tenders')
      .insert({
        ...tender,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update tender
  async updateTender(id: string, updates: Partial<TenderData>): Promise<TenderData> {
    const { data, error } = await supabase
      .from('tenders')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }
}

// Tender Meeting Services
export const tenderMeetingService = {
  // Get meetings for a tender
  async getTenderMeetings(tenderId: string): Promise<TenderMeeting[]> {
    const { data, error } = await supabase
      .from('tender_meetings')
      .select('*')
      .eq('tender_id', tenderId)
      .order('meeting_date', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Get all upcoming meetings
  async getUpcomingMeetings(): Promise<TenderMeeting[]> {
    const { data, error } = await supabase
      .from('tender_meetings')
      .select(`
        *,
        tenders!inner(title, issuer, status)
      `)
      .gte('meeting_date', new Date().toISOString().split('T')[0])
      .order('meeting_date', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Create meeting
  async createMeeting(meeting: Partial<TenderMeeting>): Promise<TenderMeeting> {
    const { data, error } = await supabase
      .from('tender_meetings')
      .insert({
        ...meeting,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update meeting
  async updateMeeting(id: string, updates: Partial<TenderMeeting>): Promise<TenderMeeting> {
    const { data, error } = await supabase
      .from('tender_meetings')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }
}

// Tender Task Services
export const tenderTaskService = {
  // Get tasks for a tender
  async getTenderTasks(tenderId: string): Promise<TenderTask[]> {
    const { data, error } = await supabase
      .from('tender_tasks')
      .select('*')
      .eq('tender_id', tenderId)
      .order('due_date', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Get tasks assigned to user
  async getUserTasks(userId: string): Promise<TenderTask[]> {
    const { data, error } = await supabase
      .from('tender_tasks')
      .select(`
        *,
        tenders!inner(title, issuer, closing_date)
      `)
      .eq('assigned_to', userId)
      .order('due_date', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Get overdue tasks
  async getOverdueTasks(): Promise<TenderTask[]> {
    const { data, error } = await supabase
      .from('tender_tasks')
      .select(`
        *,
        tenders!inner(title, issuer)
      `)
      .lt('due_date', new Date().toISOString())
      .neq('status', 'completed')
      .order('due_date', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Create task
  async createTask(task: Partial<TenderTask>): Promise<TenderTask> {
    const { data, error } = await supabase
      .from('tender_tasks')
      .insert({
        ...task,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update task
  async updateTask(id: string, updates: Partial<TenderTask>): Promise<TenderTask> {
    const { data, error } = await supabase
      .from('tender_tasks')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
        ...(updates.status === 'completed' && { completed_at: new Date().toISOString() })
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Complete task
  async completeTask(id: string): Promise<TenderTask> {
    return this.updateTask(id, {
      status: 'completed',
      completed_at: new Date().toISOString()
    })
  }
}

// Tender Bid Services
export const tenderBidService = {
  // Get all bids for current user
  async getUserBids(userId: string): Promise<TenderBid[]> {
    const { data, error } = await supabase
      .from('bids')
      .select(`
        *,
        tenders!inner(title, issuer_name, closing_date, tender_value)
      `)
      .eq('bidder_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.warn('No bids found for user:', error.message)
      return []
    }

    // Transform data to match TenderBid interface
    return (data || []).map(bid => ({
      ...bid,
      tender_title: bid.tender_title || bid.tenders?.title,
      issuer: bid.organization_name || bid.tenders?.issuer_name,
      closing_date: bid.deadline || bid.tenders?.closing_date,
      estimated_value: bid.tenders?.tender_value,
    }))
  },

  // Get bid by ID
  async getBidById(id: string): Promise<TenderBid | null> {
    const { data, error } = await supabase
      .from('bids')
      .select(`
        *,
        tenders!inner(*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.warn('Bid not found:', error.message)
      return null
    }

    return data ? {
      ...data,
      tender_title: data.tender_title || data.tenders?.title,
      issuer: data.organization_name || data.tenders?.issuer_name,
      closing_date: data.deadline || data.tenders?.closing_date,
      estimated_value: data.tenders?.tender_value,
    } : null
  },

  // Create new bid
  async createBid(bid: Partial<TenderBid>): Promise<TenderBid> {
    const { data, error } = await supabase
      .from('tender_bids')
      .insert({
        ...bid,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update bid
  async updateBid(id: string, updates: Partial<TenderBid>): Promise<TenderBid> {
    const { data, error } = await supabase
      .from('tender_bids')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Submit bid
  async submitBid(id: string): Promise<TenderBid> {
    return this.updateBid(id, {
      status: 'submitted',
      submission_date: new Date().toISOString()
    })
  },

  // Get compliance checklist for tender
  async getComplianceChecklist(tenderId: string): Promise<TenderComplianceItem[]> {
    const { data, error } = await supabase
      .from('tender_compliance_items')
      .select('*')
      .eq('tender_id', tenderId)
      .order('created_at', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Update compliance item
  async updateComplianceItem(id: string, updates: Partial<TenderComplianceItem>): Promise<TenderComplianceItem> {
    const { data, error } = await supabase
      .from('tender_compliance_items')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Generate AI analysis for bid
  async generateBidAnalysis(tenderId: string, bidAmount: number): Promise<TenderBidAIAnalysis> {
    // This would typically call an AI service
    // For now, return mock analysis
    const tender = await tenderService.getTenderById(tenderId)
    if (!tender) throw new Error('Tender not found')

    const analysis: TenderBidAIAnalysis = {
      match_score: Math.floor(Math.random() * 40) + 60, // 60-100
      win_probability: Math.floor(Math.random() * 50) + 30, // 30-80
      strengths: [
        'Strong technical capability',
        'Competitive pricing',
        'Relevant experience'
      ],
      weaknesses: [
        'Limited local presence',
        'Tight timeline'
      ],
      recommendations: [
        'Highlight previous similar projects',
        'Consider partnering with local firms',
        'Emphasize quality assurance processes'
      ],
      risk_factors: [
        'High competition expected',
        'Complex technical requirements'
      ],
      competitive_analysis: {
        expected_competitors: Math.floor(Math.random() * 8) + 3,
        market_position: 'strong',
        pricing_strategy: bidAmount < (tender.estimated_value || 0) * 0.9 ? 'aggressive' : 'competitive'
      },
      estimated_effort_hours: Math.floor(Math.random() * 200) + 100,
      suggested_bid_amount: (tender.estimated_value || 0) * (0.85 + Math.random() * 0.1),
      confidence_level: Math.floor(Math.random() * 30) + 70
    }

    return analysis
  }
}

// Filter Management Services
export const filterService = {
  // Save filter set
  async saveFilterSet(userId: string, name: string, filters: any): Promise<any> {
    const { data, error } = await supabase
      .from('saved_tender_filters')
      .insert({
        user_id: userId,
        name,
        filters,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Get user's saved filters
  async getUserFilterSets(userId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('saved_tender_filters')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  // Update filter set
  async updateFilterSet(id: string, updates: any): Promise<any> {
    const { data, error } = await supabase
      .from('saved_tender_filters')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete filter set
  async deleteFilterSet(id: string): Promise<void> {
    const { error } = await supabase
      .from('saved_tender_filters')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}
