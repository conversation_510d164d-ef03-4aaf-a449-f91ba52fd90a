@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Leaflet Map Styles */
.leaflet-container {
  height: 100%;
  width: 100%;
}

.custom-tender-marker {
  background: transparent !important;
  border: none !important;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.leaflet-popup-tip {
  background: white;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Custom scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-text-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-text-purple {
    background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 ease-in-out;
  }

  .card-hover:hover {
    @apply transform -translate-y-1 shadow-lg;
  }

  /* Button variants */
  .btn-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white;
    @apply hover:from-blue-700 hover:to-purple-700;
    @apply transition-all duration-200;
  }

  .btn-gradient-green {
    @apply bg-gradient-to-r from-green-600 to-emerald-600 text-white;
    @apply hover:from-green-700 hover:to-emerald-700;
    @apply transition-all duration-200;
  }

  .btn-gradient-orange {
    @apply bg-gradient-to-r from-orange-600 to-red-600 text-white;
    @apply hover:from-orange-700 hover:to-red-700;
    @apply transition-all duration-200;
  }

  /* Loading animations */
  .loading-dots {
    @apply inline-flex space-x-1;
  }

  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
    animation-delay: calc(var(--i) * 0.2s);
  }

  /* Status indicators */
  .status-online {
    @apply w-3 h-3 bg-green-500 rounded-full border-2 border-white;
  }

  .status-offline {
    @apply w-3 h-3 bg-gray-400 rounded-full border-2 border-white;
  }

  .status-away {
    @apply w-3 h-3 bg-yellow-500 rounded-full border-2 border-white;
  }

  .status-busy {
    @apply w-3 h-3 bg-red-500 rounded-full border-2 border-white;
  }

  /* Progress bars */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }

  .progress-fill {
    @apply h-2 bg-blue-600 rounded-full transition-all duration-300;
  }

  /* Notification styles */
  .notification-success {
    @apply bg-green-50 border-green-200 text-green-800;
  }

  .notification-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
  }

  .notification-error {
    @apply bg-red-50 border-red-200 text-red-800;
  }

  .notification-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
  }

  /* Sidebar styles */
  .sidebar-item {
    @apply flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-all duration-200;
    @apply text-slate-300 hover:bg-slate-700 hover:text-white;
  }

  .sidebar-item.active {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg;
  }

  /* Dashboard grid */
  .dashboard-grid {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .dashboard-grid-large {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  /* Responsive text */
  .text-responsive {
    @apply text-sm sm:text-base md:text-lg lg:text-xl;
  }

  .text-responsive-small {
    @apply text-xs sm:text-sm md:text-base;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset;
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Truncate text with ellipsis */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Safe area padding for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}
