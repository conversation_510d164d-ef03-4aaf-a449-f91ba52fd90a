import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Helper functions for common operations
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  if (error) throw error
  return data
}

export const signUpWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password
  })
  if (error) throw error
  return data
}

// Database types (these should match your Supabase schema)
export type Database = {
  public: {
    Tables: {
      tenders: {
        Row: {
          id: string
          title: string
          description: string
          issuer: string
          category: string
          status: string
          publish_date: string
          closing_date: string
          estimated_value: number
          currency: string
          location: string
          requirements: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          issuer: string
          category: string
          status: string
          publish_date: string
          closing_date: string
          estimated_value: number
          currency: string
          location: string
          requirements: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          issuer?: string
          category?: string
          status?: string
          publish_date?: string
          closing_date?: string
          estimated_value?: number
          currency?: string
          location?: string
          requirements?: string[]
          created_at?: string
          updated_at?: string
        }
      }
      bids: {
        Row: {
          id: string
          tender_id: string
          title: string
          status: string
          amount: number
          currency: string
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tender_id: string
          title: string
          status: string
          amount: number
          currency: string
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tender_id?: string
          title?: string
          status?: string
          amount?: number
          currency?: string
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          name: string
          email: string
          role: string
          department: string
          avatar: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email: string
          role: string
          department: string
          avatar?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          role?: string
          department?: string
          avatar?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
