import { createClient } from '@supabase/supabase-js'

const supabaseUrl = (import.meta as any).env.VITE_SUPABASE_URL
const supabaseAnonKey = (import.meta as any).env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Helper functions for common operations
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  if (error) throw error
  return data
}

export const signUpWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password
  })
  if (error) throw error
  return data
}

// Database types based on existing Supabase schema
export interface BeeProfile {
  id: number
  user_id: string
  full_name: string
  phone?: string
  transport_mode: string
  max_range_km: number
  rating: number
  is_enterprise_bee: boolean
  is_active?: boolean
  is_verified?: boolean
  availability_status?: string
  performance_score?: number
  total_earnings?: number
  last_heartbeat?: string
  risk_score?: number
  skills?: string
  preferred_categories?: string
  created_at: string
}

export interface TransportBooking {
  id: number
  created_by?: string
  bee_full_name: string
  service_type?: string
  pickup_address: string
  dropoff_address: string
  pickup_time: string
  estimated_fare?: number
  actual_fare?: number
  tracking_link?: string
  status?: string
  created_at?: string
  user_id?: string
  tenant_id?: string
  booking_reference?: string
  pickup_latitude?: number
  pickup_longitude?: number
  dropoff_latitude?: number
  dropoff_longitude?: number
  pickup_contact_name?: string
  pickup_contact_phone?: string
  dropoff_contact_name?: string
  dropoff_contact_phone?: string
  package_description?: string
  package_weight_kg?: number
  special_instructions?: string
  priority_level?: string
  distance_km?: number
  duration_minutes?: number
  payment_status?: string
  rating?: number
  feedback?: string
  updated_at?: string
}

export interface BeeCourierAssignment {
  id: string
  tenant_id: string
  bee_user_id: string
  bee_full_name: string
  bee_phone: string
  bee_transport_mode: string
  bee_max_range_km: number
  bee_current_rating?: number
  courier_request_id: string
  assignment_type?: string
  assigned_at?: string
  accepted_at?: string
  started_at?: string
  completed_at?: string
  cancelled_at?: string
  assignment_status?: string
  cancellation_reason?: string
  pickup_latitude?: number
  pickup_longitude?: number
  delivery_latitude?: number
  delivery_longitude?: number
  estimated_distance_km?: number
  actual_distance_km?: number
  estimated_duration_minutes?: number
  actual_duration_minutes?: number
  required_transport_mode?: string
  required_max_range_km?: number
  weight_kg?: number
  special_requirements?: string[]
  on_time_pickup?: boolean
  on_time_delivery?: boolean
  customer_satisfaction_score?: number
  performance_notes?: string
  offered_amount?: number
  final_amount?: number
  currency?: string
  payment_status?: string
  related_tender_id?: string
  tender_delivery_deadline?: string
  tender_priority_score?: number
  ai_recommended?: boolean
  recommendation_score?: number
  route_optimized?: boolean
  optimization_group_id?: string
  created_at?: string
  updated_at?: string
  assigned_by?: string
}

export interface BeeTask {
  id: string
  task_id?: string
  bidder_id?: string
  bee_id?: string
  tender_id?: string
  title: string
  description?: string
  category: string
  task_type?: string
  pickup_address?: string
  delivery_address?: string
  pickup_latitude?: number
  pickup_longitude?: number
  delivery_latitude?: number
  delivery_longitude?: number
  estimated_distance_km?: number
  estimated_duration_minutes?: number
  payment_amount: number
  cost?: number
  commission_amount?: number
  status?: string
  priority?: string
  deadline?: string
  required_skills?: string
  special_instructions?: string
  proof_requirements?: string
  proof_uploads?: string
  is_urgent?: boolean
  is_fragile?: boolean
  weight_kg?: number
  assigned_at?: string
  started_at?: string
  completed_at?: string
  created_at?: string
  updated_at?: string
}

export interface TenderData {
  // Core database fields (matching actual schema)
  id: string
  tender_number?: string
  title: string
  description?: string
  issuer_name?: string
  issuer_type?: string
  publish_date?: string
  closing_date?: string
  category_code?: string
  cpv_code?: string
  document_available?: boolean
  document_link?: string
  documents_uploaded?: boolean
  risk_score?: number
  institution_risk_score?: number
  success_prediction?: number
  parsed_by?: string
  original_data?: any
  scm_contact_name?: string
  scm_contact_number?: string
  scm_contact_email?: string
  technical_enquiries_name?: string
  technical_enquiries_contact?: string
  technical_enquiries_email?: string
  province?: string
  city?: string
  address?: string
  delivery_address?: string
  tender_value?: number
  currency?: string
  procurement_method?: string
  procurement_method_details?: string
  should_include_in_main?: boolean
  created_at: string
  updated_at?: string

  // Computed/legacy fields for backward compatibility
  issuer?: string // Maps to issuer_name
  estimated_value?: number // Maps to tender_value
  category?: string // Maps to category_code
  location?: string // Computed from province + city
  status?: string // Computed field
  latitude?: number // For map functionality (to be added)
  longitude?: number // For map functionality (to be added)
  ai_match_score?: number // Maps to success_prediction
  risk_level?: string // Computed from risk_score
  documents?: TenderDocument[]
  requirements?: string[]
}

export interface TenderDocument {
  id: string
  tender_id: string
  name: string
  type: string
  url?: string
  file_path?: string
  file_size?: number
  uploaded_at: string
  processed?: boolean
  extracted_content?: string
}

export interface TenderMeeting {
  id: string
  tender_id: string
  meeting_type: string // 'briefing', 'site_visit', 'clarification', 'submission'
  title: string
  description?: string
  meeting_date: string
  meeting_time?: string
  location?: string
  latitude?: number
  longitude?: number
  is_compulsory: boolean
  contact_person?: string
  contact_details?: string
  agenda?: string[]
  attendees_required?: string[]
  created_at: string
  updated_at: string
}

export interface TenderTask {
  id: string
  tender_id: string
  task_type: string // 'document_preparation', 'site_visit', 'submission', 'follow_up'
  title: string
  description?: string
  assigned_to?: string // user_id
  assigned_to_role?: string // 'supplier', 'bee_runner', 'admin'
  status: string // 'pending', 'in_progress', 'completed', 'overdue'
  priority: string // 'low', 'medium', 'high', 'critical'
  due_date: string
  estimated_hours?: number
  actual_hours?: number
  dependencies?: string[] // array of task_ids
  checklist_items?: TenderTaskChecklistItem[]
  notes?: string
  created_at: string
  updated_at: string
  completed_at?: string
}

export interface TenderTaskChecklistItem {
  id: string
  task_id: string
  item_text: string
  is_completed: boolean
  completed_by?: string
  completed_at?: string
  notes?: string
}

export interface TenderBid {
  id: string
  tender_id: string
  bidder_id: string
  bid_amount: number
  currency: string
  status: string // 'draft', 'submitted', 'shortlisted', 'awarded', 'rejected'
  submission_date?: string
  validity_period_days?: number
  technical_score?: number
  commercial_score?: number
  total_score?: number
  ranking?: number
  documents?: TenderBidDocument[]
  team_members?: TenderBidTeamMember[]
  compliance_checklist?: TenderComplianceItem[]
  ai_analysis?: TenderBidAIAnalysis
  created_at: string
  updated_at: string
}

export interface TenderBidDocument {
  id: string
  bid_id: string
  document_type: string
  name: string
  file_path?: string
  url?: string
  version: number
  status: string // 'draft', 'final', 'submitted'
  uploaded_by: string
  uploaded_at: string
}

export interface TenderBidTeamMember {
  id: string
  bid_id: string
  user_id: string
  name: string
  role: string
  expertise: string[]
  cv_document_id?: string
  hourly_rate?: number
  allocated_hours?: number
}

export interface TenderComplianceItem {
  id: string
  tender_id?: string
  bid_id?: string
  requirement_text: string
  is_compliant: boolean
  evidence_provided?: string
  document_reference?: string
  notes?: string
  verified_by?: string
  verified_at?: string
}

export interface TenderBidAIAnalysis {
  match_score: number
  win_probability: number
  strengths: string[]
  weaknesses: string[]
  recommendations: string[]
  risk_factors: string[]
  competitive_analysis?: {
    expected_competitors: number
    market_position: string
    pricing_strategy: string
  }
  estimated_effort_hours: number
  suggested_bid_amount: number
  confidence_level: number
}
