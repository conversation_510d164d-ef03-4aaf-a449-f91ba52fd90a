import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Helper functions for common operations
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  if (error) throw error
  return data
}

export const signUpWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password
  })
  if (error) throw error
  return data
}

// Database types based on existing Supabase schema
export interface BeeProfile {
  id: number
  user_id: string
  full_name: string
  phone?: string
  transport_mode: string
  max_range_km: number
  rating: number
  is_enterprise_bee: boolean
  is_active?: boolean
  is_verified?: boolean
  availability_status?: string
  performance_score?: number
  total_earnings?: number
  last_heartbeat?: string
  risk_score?: number
  skills?: string
  preferred_categories?: string
  created_at: string
}

export interface TransportBooking {
  id: number
  created_by?: string
  bee_full_name: string
  service_type?: string
  pickup_address: string
  dropoff_address: string
  pickup_time: string
  estimated_fare?: number
  actual_fare?: number
  tracking_link?: string
  status?: string
  created_at?: string
  user_id?: string
  tenant_id?: string
  booking_reference?: string
  pickup_latitude?: number
  pickup_longitude?: number
  dropoff_latitude?: number
  dropoff_longitude?: number
  pickup_contact_name?: string
  pickup_contact_phone?: string
  dropoff_contact_name?: string
  dropoff_contact_phone?: string
  package_description?: string
  package_weight_kg?: number
  special_instructions?: string
  priority_level?: string
  distance_km?: number
  duration_minutes?: number
  payment_status?: string
  rating?: number
  feedback?: string
  updated_at?: string
}

export interface BeeCourierAssignment {
  id: string
  tenant_id: string
  bee_user_id: string
  bee_full_name: string
  bee_phone: string
  bee_transport_mode: string
  bee_max_range_km: number
  bee_current_rating?: number
  courier_request_id: string
  assignment_type?: string
  assigned_at?: string
  accepted_at?: string
  started_at?: string
  completed_at?: string
  cancelled_at?: string
  assignment_status?: string
  cancellation_reason?: string
  pickup_latitude?: number
  pickup_longitude?: number
  delivery_latitude?: number
  delivery_longitude?: number
  estimated_distance_km?: number
  actual_distance_km?: number
  estimated_duration_minutes?: number
  actual_duration_minutes?: number
  required_transport_mode?: string
  required_max_range_km?: number
  weight_kg?: number
  special_requirements?: string[]
  on_time_pickup?: boolean
  on_time_delivery?: boolean
  customer_satisfaction_score?: number
  performance_notes?: string
  offered_amount?: number
  final_amount?: number
  currency?: string
  payment_status?: string
  related_tender_id?: string
  tender_delivery_deadline?: string
  tender_priority_score?: number
  ai_recommended?: boolean
  recommendation_score?: number
  route_optimized?: boolean
  optimization_group_id?: string
  created_at?: string
  updated_at?: string
  assigned_by?: string
}

export interface BeeTask {
  id: string
  task_id?: string
  bidder_id?: string
  bee_id?: string
  tender_id?: string
  title: string
  description?: string
  category: string
  task_type?: string
  pickup_address?: string
  delivery_address?: string
  pickup_latitude?: number
  pickup_longitude?: number
  delivery_latitude?: number
  delivery_longitude?: number
  estimated_distance_km?: number
  estimated_duration_minutes?: number
  payment_amount: number
  cost?: number
  commission_amount?: number
  status?: string
  priority?: string
  deadline?: string
  required_skills?: string
  special_instructions?: string
  proof_requirements?: string
  proof_uploads?: string
  is_urgent?: boolean
  is_fragile?: boolean
  weight_kg?: number
  assigned_at?: string
  started_at?: string
  completed_at?: string
  created_at?: string
  updated_at?: string
}

export interface TenderData {
  id: string
  title: string
  description?: string
  category?: string
  status: string
  publish_date?: string
  closing_date?: string
  estimated_value?: number
  currency?: string
  location?: string
  requirements?: string[]
  created_at: string
  updated_at: string
}
