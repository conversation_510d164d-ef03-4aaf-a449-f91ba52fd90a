import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import TMSLayout from '@/components/layout/TMSLayout';
import BiddingDashboard from '@/components/dashboard/BiddingDashboard';
import SmartTenderCard from '@/components/tenders/SmartTenderCard';
import BidSimulator from '@/components/bids/BidSimulator';
import VirtualWarRoom from '@/components/collaboration/VirtualWarRoom';
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';
import DatabaseDashboard from './components/DatabaseDashboard';
import ApiTestPanel from './components/ApiTestPanel';
import TenderDiscoveryPage from './pages/TenderDiscoveryPage';
import PlannerPage from './pages/PlannerPage';
import BidManagementPage from './pages/BidManagementPage';
import { supabase } from './lib/supabase';
import { beeService, transportService, courierService, analyticsService } from './services/api';
import {
  User,
  Tender,
  Bid,
  Analytics,
  Notification,
  UserRole,
  TenderCategory,
  TenderStatus,
  BidStatus,
  RiskLevel
} from '@/types';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Mock data for demonstration
const mockUser: User = {
  id: '1',
  name: 'John Smith',
  email: '<EMAIL>',
  role: UserRole.MANAGER,
  avatar: '/avatars/john.jpg',
  department: 'Business Development',
  permissions: ['view_tenders', 'create_bids', 'manage_team', 'view_analytics'],
  preferences: {
    theme: 'light',
    dashboard: {
      layout: 'grid',
      defaultView: 'dashboard',
      widgets: ['smart-tenders', 'ai-insights', 'recent-activity'],
    },
    notifications: {
      email: true,
      push: true,
      inApp: true,
    },
  },
};

const mockTenders: Tender[] = [
  {
    id: '1',
    title: 'Municipal Infrastructure Development Project',
    description: 'Comprehensive infrastructure development including roads, water systems, and digital connectivity for the Greater Cape Town metropolitan area.',
    issuer: 'City of Cape Town',
    category: TenderCategory.CONSTRUCTION,
    status: TenderStatus.OPEN,
    publishDate: new Date('2024-01-15'),
    closingDate: new Date('2024-02-28'),
    estimatedValue: 15000000,
    currency: 'ZAR',
    location: 'Cape Town, Western Cape',
    requirements: ['CIDB Grade 9 CE', 'ISO 9001:2015', 'BEE Level 4 or better'],
    documents: [],
    aiInsights: {
      matchScore: 87,
      winProbability: 72,
      competitiveAnalysis: {
        expectedCompetitors: 8,
        marketPosition: 'strong',
        pricingStrategy: 'competitive',
        differentiators: ['Local presence', 'Previous municipal work', 'Strong BEE credentials'],
      },
      recommendations: [
        'Highlight your previous municipal infrastructure projects',
        'Emphasize local job creation and skills development',
        'Consider partnering with local SMMEs for BEE compliance',
      ],
      riskFactors: ['Tight timeline', 'Complex regulatory requirements'],
      opportunities: ['Long-term maintenance contract potential', 'Portfolio expansion'],
      estimatedEffort: 240,
      suggestedBidPrice: 14200000,
      confidenceLevel: 85,
    },
    complianceScore: 92,
    matchScore: 87,
    riskLevel: RiskLevel.MEDIUM,
    tags: ['infrastructure', 'municipal', 'construction', 'cape-town'],
  },
  {
    id: '2',
    title: 'Digital Transformation Services for Government Department',
    description: 'End-to-end digital transformation including cloud migration, system integration, and staff training for a major government department.',
    issuer: 'Department of Home Affairs',
    category: TenderCategory.IT_SERVICES,
    status: TenderStatus.OPEN,
    publishDate: new Date('2024-01-20'),
    closingDate: new Date('2024-03-15'),
    estimatedValue: 8500000,
    currency: 'ZAR',
    location: 'Pretoria, Gauteng',
    requirements: ['ISO 27001', 'SITA accreditation', 'Minimum 5 years government experience'],
    documents: [],
    aiInsights: {
      matchScore: 94,
      winProbability: 85,
      competitiveAnalysis: {
        expectedCompetitors: 5,
        marketPosition: 'strong',
        pricingStrategy: 'premium',
        differentiators: ['Government expertise', 'Security clearance', 'Local development team'],
      },
      recommendations: [
        'Showcase your government digital transformation portfolio',
        'Highlight security and compliance expertise',
        'Propose phased implementation approach',
      ],
      riskFactors: ['Security clearance requirements', 'Government approval processes'],
      opportunities: ['Multi-year support contract', 'Other department referrals'],
      estimatedEffort: 180,
      suggestedBidPrice: 8200000,
      confidenceLevel: 92,
    },
    complianceScore: 96,
    matchScore: 94,
    riskLevel: RiskLevel.LOW,
    tags: ['digital-transformation', 'government', 'cloud', 'security'],
  },
];

const mockBids: Bid[] = [
  {
    id: '1',
    tenderId: '1',
    title: 'Municipal Infrastructure Development Project',
    status: BidStatus.IN_REVIEW,
    amount: 14200000,
    currency: 'ZAR',
    team: [
      {
        id: '1',
        name: 'Sarah Johnson',
        role: 'Project Manager',
        email: '<EMAIL>',
        expertise: ['Project Management', 'Infrastructure'],
        availability: 100,
      },
      {
        id: '2',
        name: 'Mike Chen',
        role: 'Technical Lead',
        email: '<EMAIL>',
        expertise: ['Engineering', 'Construction'],
        availability: 80,
      },
    ],
    documents: [
      {
        id: '1',
        name: 'Technical Proposal',
        type: 'proposal',
        content: 'Technical proposal content...',
        lastModified: new Date(),
        author: 'Sarah Johnson',
        version: 1,
        status: 'draft',
      },
    ],
    timeline: [
      {
        id: '1',
        task: 'Complete technical specifications',
        assignee: 'Mike Chen',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        status: 'in_progress',
        dependencies: [],
      },
    ],
    compliance: [
      {
        id: '1',
        requirement: 'CIDB Grade 9 CE',
        status: 'compliant',
        evidence: 'Certificate attached',
      },
    ],
    aiAnalysis: {
      winProbability: 72,
      strengthsWeaknesses: {
        strengths: ['Strong technical team', 'Local presence', 'Previous experience'],
        weaknesses: ['Tight timeline', 'Resource constraints'],
      },
      improvementSuggestions: ['Add more senior resources', 'Strengthen risk mitigation'],
      riskAssessment: ['Timeline risk', 'Resource availability'],
      competitivePosition: 'Strong position with good win probability',
    },
    collaborators: [
      {
        id: '1',
        name: 'Sarah Johnson',
        role: 'Project Manager',
        isOnline: true,
        lastSeen: new Date(),
        permissions: ['view', 'edit', 'approve'],
      },
    ],
    version: 1,
    lastModified: new Date(),
  },
];

const mockAnalytics: Analytics = {
  winRate: 78,
  totalBids: 45,
  activeBids: 12,
  revenue: 25000000,
  trends: [
    { period: 'Q1', value: 65, change: 5, metric: 'winRate' },
    { period: 'Q2', value: 72, change: 7, metric: 'winRate' },
    { period: 'Q3', value: 78, change: 6, metric: 'winRate' },
  ],
  performance: {
    averageBidTime: 14,
    successRate: 78,
    clientSatisfaction: 92,
    teamEfficiency: 88,
  },
  benchmarks: {
    industry: { winRate: 65, averageBidTime: 18 },
    competitors: { winRate: 70, averageBidTime: 16 },
    historical: { winRate: 72, averageBidTime: 15 },
  },
};

const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'New Tender Alert',
    message: 'High-match tender available: Digital Transformation Services',
    type: 'tender_alert',
    priority: 'high',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: false,
  },
  {
    id: '2',
    title: 'Bid Deadline Reminder',
    message: 'Municipal Infrastructure bid due in 3 days',
    type: 'deadline_reminder',
    priority: 'urgent',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    read: false,
  },
];

function App() {
  // Initialize currentPath from browser URL
  const [currentPath, setCurrentPath] = useState(() => {
    const path = window.location.pathname;
    return path === '/' ? '/dashboard' : path;
  });
  const [user] = useState<User>(mockUser);
  const [tenders] = useState<Tender[]>(mockTenders);
  const [bids] = useState<Bid[]>(mockBids);
  const [analytics] = useState<Analytics>(mockAnalytics);
  const [notifications] = useState<Notification[]>(mockNotifications);
  const [isSupabaseConnected, setIsSupabaseConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [dbMetrics, setDbMetrics] = useState<any>(null);

  // Sync with browser URL changes
  useEffect(() => {
    const handlePopState = () => {
      const path = window.location.pathname;
      setCurrentPath(path === '/' ? '/dashboard' : path);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Test Supabase connection and load real data
  useEffect(() => {
    const testConnection = async () => {
      try {
        // Test connection with a simple query
        const { data, error } = await supabase
          .from('bee_profiles')
          .select('count')
          .limit(1);

        if (error) {
          setConnectionError(error.message);
          setIsSupabaseConnected(false);
        } else {
          setIsSupabaseConnected(true);
          setConnectionError(null);

          // Load dashboard metrics from real database
          try {
            const metrics = await analyticsService.getDashboardMetrics();
            setDbMetrics(metrics);
          } catch (metricsError) {
            console.warn('Could not load database metrics:', metricsError);
          }
        }
      } catch (err) {
        setConnectionError('Failed to connect to Supabase database');
        setIsSupabaseConnected(false);
      }
    };

    testConnection();
  }, []);

  const handleNavigate = (path: string) => {
    setCurrentPath(path);
    // Update browser URL without page reload
    window.history.pushState({}, '', path);
  };

  const handleTenderClick = (tender: Tender) => {
    console.log('Tender clicked:', tender.title);
    // Navigate to tender detail page
  };

  const handleBidClick = (bid: Bid) => {
    console.log('Bid clicked:', bid.title);
    // Navigate to bid detail page
  };

  const handleStartBid = (tender: Tender) => {
    console.log('Start bid for:', tender.title);
    // Navigate to bid creation page
  };

  const renderCurrentPage = () => {
    switch (currentPath) {
      case '/dashboard':
        return (
          <BiddingDashboard
            user={user}
            tenders={tenders}
            bids={bids}
            analytics={analytics}
            onTenderClick={handleTenderClick}
            onBidClick={handleBidClick}
            onStartBid={handleStartBid}
          />
        );
      case '/tenders':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-6">Tender Management</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tenders.map((tender) => (
                <SmartTenderCard
                  key={tender.id}
                  tender={tender}
                  onClick={() => handleTenderClick(tender)}
                  onStartBid={() => handleStartBid(tender)}
                />
              ))}
            </div>
          </div>
        );
      case '/tender-discovery':
        return <TenderDiscoveryPage />;
      case '/planner':
        return <PlannerPage />;
      case '/bids':
        return <BidManagementPage />;
      case '/analytics':
        return (
          <div className="p-6">
            <AnalyticsDashboard
              analytics={analytics}
              tenders={tenders}
              bids={bids}
            />
          </div>
        );
      case '/collaboration':
        return (
          <div className="p-6">
            <VirtualWarRoom
              bids={bids}
              user={user}
            />
          </div>
        );
      case '/ai-insights':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-6">AI Insights</h1>
            <p className="text-slate-600">AI insights interface would go here...</p>
          </div>
        );
      case '/database':
        return (
          <div className="p-6">
            <DatabaseDashboard isConnected={isSupabaseConnected} />
          </div>
        );
      case '/api-test':
        return (
          <div className="p-6">
            <ApiTestPanel />
          </div>
        );
      default:
        return (
          <BiddingDashboard
            user={user}
            tenders={tenders}
            bids={bids}
            analytics={analytics}
            onTenderClick={handleTenderClick}
            onBidClick={handleBidClick}
            onStartBid={handleStartBid}
          />
        );
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div className="App">
        {/* Connection Status Banner */}
        {!isSupabaseConnected && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  Database Connection Error: {connectionError || 'Unable to connect to Supabase'}
                </p>
                <p className="text-xs text-red-600 mt-1">
                  Some features may not work properly. Check your environment configuration.
                </p>
              </div>
            </div>
          </div>
        )}

        {isSupabaseConnected && (
          <div className="bg-green-50 border-l-4 border-green-400 p-2">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-2">
                <p className="text-sm text-green-700">
                  Connected to BidBees Database
                  {dbMetrics && (
                    <span className="ml-2 text-xs">
                      ({dbMetrics.bees?.length || 0} bees, {dbMetrics.bookings?.length || 0} bookings)
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        <TMSLayout
          user={user}
          notifications={notifications}
          onNavigate={handleNavigate}
          currentPath={currentPath}
        >
          {renderCurrentPage()}
        </TMSLayout>
        <Toaster />
      </div>
    </QueryClientProvider>
  );
}

export default App;
