import React, { useState } from 'react';
import { beeService, transportService, courierService } from '../services/api';
import { supabase } from '../lib/supabase';

const ApiTestPanel: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test: string, success: boolean, data?: any, error?: any) => {
    const result = {
      test,
      success,
      data: success ? data : null,
      error: success ? null : error?.message || error,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
  };

  const testSupabaseConnection = async () => {
    try {
      const { data, error } = await supabase
        .from('bee_profiles')
        .select('count')
        .limit(1);
      
      if (error) throw error;
      addResult('Supabase Connection', true, 'Connected successfully');
    } catch (error) {
      addResult('Supabase Connection', false, null, error);
    }
  };

  const testBeeService = async () => {
    try {
      const bees = await beeService.getAllBees();
      addResult('Bee Service - Get All', true, `Found ${bees.length} bees`);
    } catch (error) {
      addResult('Bee Service - Get All', false, null, error);
    }
  };

  const testTransportService = async () => {
    try {
      const bookings = await transportService.getAllBookings();
      addResult('Transport Service - Get All', true, `Found ${bookings.length} bookings`);
    } catch (error) {
      addResult('Transport Service - Get All', false, null, error);
    }
  };

  const testCourierService = async () => {
    try {
      const assignments = await courierService.getAllAssignments();
      addResult('Courier Service - Get All', true, `Found ${assignments.length} assignments`);
    } catch (error) {
      addResult('Courier Service - Get All', false, null, error);
    }
  };

  const testCreateBee = async () => {
    try {
      // This is a test - we won't actually create a bee, just test the API structure
      const testBee = {
        user_id: 'test-user-id',
        full_name: 'Test Bee',
        transport_mode: 'bicycle',
        max_range_km: 10,
        rating: 5.0,
        is_enterprise_bee: false
      };
      
      // Instead of creating, let's just test the query structure
      const { data, error } = await supabase
        .from('bee_profiles')
        .select('*')
        .limit(1);
      
      if (error) throw error;
      addResult('Bee Creation Test (Structure)', true, 'API structure valid');
    } catch (error) {
      addResult('Bee Creation Test (Structure)', false, null, error);
    }
  };

  const runAllTests = async () => {
    setLoading(true);
    setTestResults([]);
    
    await testSupabaseConnection();
    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
    
    await testBeeService();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testTransportService();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testCourierService();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testCreateBee();
    
    setLoading(false);
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900">API Test Panel</h2>
        <button
          onClick={runAllTests}
          disabled={loading}
          className={`px-4 py-2 rounded font-medium ${
            loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {loading ? 'Running Tests...' : 'Run All Tests'}
        </button>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <button
          onClick={testSupabaseConnection}
          disabled={loading}
          className="bg-green-100 text-green-800 px-3 py-2 rounded text-sm hover:bg-green-200 disabled:opacity-50"
        >
          Test Connection
        </button>
        <button
          onClick={testBeeService}
          disabled={loading}
          className="bg-blue-100 text-blue-800 px-3 py-2 rounded text-sm hover:bg-blue-200 disabled:opacity-50"
        >
          Test Bees API
        </button>
        <button
          onClick={testTransportService}
          disabled={loading}
          className="bg-purple-100 text-purple-800 px-3 py-2 rounded text-sm hover:bg-purple-200 disabled:opacity-50"
        >
          Test Transport API
        </button>
        <button
          onClick={testCourierService}
          disabled={loading}
          className="bg-orange-100 text-orange-800 px-3 py-2 rounded text-sm hover:bg-orange-200 disabled:opacity-50"
        >
          Test Courier API
        </button>
      </div>

      <div className="space-y-3">
        <h3 className="text-lg font-medium text-gray-900">Test Results</h3>
        {testResults.length === 0 ? (
          <p className="text-gray-500 text-sm">No tests run yet. Click "Run All Tests" to start.</p>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded border-l-4 ${
                  result.success
                    ? 'bg-green-50 border-green-400'
                    : 'bg-red-50 border-red-400'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className={`font-medium ${
                      result.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {result.test}
                    </p>
                    {result.data && (
                      <p className="text-sm text-gray-600 mt-1">{result.data}</p>
                    )}
                    {result.error && (
                      <p className="text-sm text-red-600 mt-1">{result.error}</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">{result.timestamp}</span>
                    <span className={`text-lg ${
                      result.success ? 'text-green-500' : 'text-red-500'
                    }`}>
                      {result.success ? '✓' : '✗'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded">
        <h4 className="font-medium text-gray-900 mb-2">Environment Status</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Supabase URL:</span>
            <span className="ml-2 font-mono text-xs">
              {(import.meta as any).env.VITE_SUPABASE_URL ? '✓ Set' : '✗ Missing'}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Supabase Key:</span>
            <span className="ml-2 font-mono text-xs">
              {(import.meta as any).env.VITE_SUPABASE_ANON_KEY ? '✓ Set' : '✗ Missing'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiTestPanel;
