import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Target,
  CheckSquare,
  FileText,
  DollarSign,
  Users,
  Calendar,
  Clock,
  AlertTriangle,
  Info,
  ChevronRight,
  ChevronLeft,
  Save,
  Send,
  Sparkles,
  Building,
  MapPin,
  Phone,
  Mail,
  Globe,
  Award,
  Briefcase
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { tenderBidService } from '../../services/api';
import type { TenderData, TenderBid } from '../../lib/supabase';

interface BidCreationWizardProps {
  tender: TenderData;
  onComplete: (bid: TenderBid) => void;
  onCancel: () => void;
  existingBid?: TenderBid;
}

interface BidFormData {
  // Basic Information
  bidAmount: number;
  currency: string;
  validityPeriod: number;

  // Company Information
  companyName: string;
  registrationNumber: string;
  vatNumber: string;
  beeLevel: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;

  // Technical Proposal
  technicalApproach: string;
  methodology: string;
  timeline: string;
  deliverables: string;

  // Team & Resources
  teamMembers: Array<{
    name: string;
    role: string;
    experience: string;
    cv?: File;
  }>;

  // Compliance & Requirements
  complianceItems: Array<{
    id: string;
    requirement: string;
    isCompliant: boolean;
    evidence?: string;
    document?: File;
  }>;

  // Additional Information
  additionalInfo: string;
  assumptions: string;
  exclusions: string;
}

const WIZARD_STEPS = [
  { id: 'overview', title: 'Overview', icon: Info },
  { id: 'company', title: 'Company Info', icon: Building },
  { id: 'technical', title: 'Technical', icon: FileText },
  { id: 'team', title: 'Team', icon: Users },
  { id: 'compliance', title: 'Compliance', icon: CheckSquare },
  { id: 'pricing', title: 'Pricing', icon: DollarSign },
  { id: 'review', title: 'Review', icon: Target }
];

const BidCreationWizard: React.FC<BidCreationWizardProps> = ({
  tender,
  onComplete,
  onCancel,
  existingBid
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<BidFormData>({
    bidAmount: 0,
    currency: 'ZAR',
    validityPeriod: 30,
    companyName: '',
    registrationNumber: '',
    vatNumber: '',
    beeLevel: '',
    contactPerson: '',
    email: '',
    phone: '',
    address: '',
    technicalApproach: '',
    methodology: '',
    timeline: '',
    deliverables: '',
    teamMembers: [],
    complianceItems: [],
    additionalInfo: '',
    assumptions: '',
    exclusions: ''
  });
  const [loading, setLoading] = useState(false);
  const [autoFillEnabled, setAutoFillEnabled] = useState(true);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    if (existingBid) {
      // Load existing bid data
      loadExistingBidData();
    } else {
      // Auto-fill with user profile and tender data
      autoFillFormData();
    }

    // Initialize compliance items from tender requirements
    initializeComplianceItems();
  }, [tender, existingBid]);

  const loadExistingBidData = () => {
    // Load data from existing bid
    if (existingBid) {
      setFormData(prev => ({
        ...prev,
        bidAmount: existingBid.bid_amount,
        currency: existingBid.currency,
        // ... other fields from existing bid
      }));
    }
  };

  const autoFillFormData = () => {
    if (!autoFillEnabled) return;

    // Auto-fill from user profile (this would come from a user service)
    const userProfile = {
      companyName: 'BidBees Construction Ltd',
      registrationNumber: '2023/123456/07',
      vatNumber: '4123456789',
      beeLevel: 'Level 4',
      contactPerson: 'John Smith',
      email: '<EMAIL>',
      phone: '+27 11 123 4567',
      address: '123 Business Park, Johannesburg, 2000'
    };

    setFormData(prev => ({
      ...prev,
      ...userProfile,
      // Auto-suggest bid amount based on tender estimated value
      bidAmount: tender.estimated_value ? tender.estimated_value * 0.95 : 0
    }));
  };

  const initializeComplianceItems = () => {
    // Extract compliance requirements from tender
    const requirements = [
      'Valid Tax Clearance Certificate',
      'Company Registration Certificate',
      'BEE Certificate',
      'Professional Indemnity Insurance',
      'Public Liability Insurance',
      'Health & Safety Policy',
      'Quality Management System Certificate'
    ];

    if (tender.compulsory_briefing) {
      requirements.push('Attendance at Compulsory Briefing');
    }

    if (tender.performance_guarantee_required) {
      requirements.push('Performance Guarantee');
    }

    const complianceItems = requirements.map((req, index) => ({
      id: `req-${index}`,
      requirement: req,
      isCompliant: false,
      evidence: ''
    }));

    setFormData(prev => ({
      ...prev,
      complianceItems
    }));
  };

  const updateFormData = (updates: Partial<BidFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const validateCurrentStep = (): boolean => {
    const errors: string[] = [];

    switch (WIZARD_STEPS[currentStep].id) {
      case 'company':
        if (!formData.companyName) errors.push('Company name is required');
        if (!formData.registrationNumber) errors.push('Registration number is required');
        if (!formData.contactPerson) errors.push('Contact person is required');
        if (!formData.email) errors.push('Email is required');
        break;
      case 'pricing':
        if (formData.bidAmount <= 0) errors.push('Bid amount must be greater than 0');
        break;
      case 'compliance':
        const requiredCompliance = formData.complianceItems.filter(item =>
          item.requirement.includes('Tax Clearance') ||
          item.requirement.includes('Registration')
        );
        if (requiredCompliance.some(item => !item.isCompliant)) {
          errors.push('Critical compliance items must be completed');
        }
        break;
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const nextStep = () => {
    if (validateCurrentStep() && currentStep < WIZARD_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const saveDraft = async () => {
    try {
      setLoading(true);

      const bidData = {
        tender_id: tender.id,
        bidder_id: 'current-user-id', // This would be dynamic
        bid_amount: formData.bidAmount,
        currency: formData.currency,
        status: 'draft',
        technical_proposal: {
          approach: formData.technicalApproach,
          methodology: formData.methodology,
          timeline: formData.timeline,
          deliverables: formData.deliverables
        },
        team_members: formData.teamMembers,
        compliance_checklist: formData.complianceItems
      };

      const savedBid = existingBid
        ? await tenderBidService.updateBid(existingBid.id, bidData)
        : await tenderBidService.createBid(bidData);

      onComplete(savedBid);
    } catch (error) {
      console.error('Failed to save bid:', error);
    } finally {
      setLoading(false);
    }
  };

  const submitBid = async () => {
    try {
      setLoading(true);

      // First save as draft
      await saveDraft();

      // Then submit
      if (existingBid) {
        await tenderBidService.submitBid(existingBid.id);
      }
    } catch (error) {
      console.error('Failed to submit bid:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStepProgress = () => {
    return ((currentStep + 1) / WIZARD_STEPS.length) * 100;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: formData.currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStepContent = () => {
    const stepId = WIZARD_STEPS[currentStep].id;

    switch (stepId) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Target className="w-16 h-16 text-blue-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Create Your Bid</h2>
              <p className="text-gray-600">
                Let's help you create a comprehensive bid for this tender
              </p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Tender Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium">{tender.title}</h4>
                  <p className="text-sm text-gray-600">{tender.issuer}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Estimated Value</p>
                    <p className="font-semibold">
                      {tender.estimated_value ? formatCurrency(tender.estimated_value) : 'Not specified'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Closing Date</p>
                    <p className="font-semibold">{formatDate(tender.closing_date)}</p>
                  </div>
                </div>

                {tender.description && (
                  <div>
                    <p className="text-sm text-gray-600">Description</p>
                    <p className="text-sm">{tender.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex items-center space-x-2">
              <Switch
                id="auto-fill"
                checked={autoFillEnabled}
                onCheckedChange={setAutoFillEnabled}
              />
              <Label htmlFor="auto-fill" className="flex items-center space-x-2">
                <Sparkles className="w-4 h-4" />
                <span>Enable auto-fill from your profile</span>
              </Label>
            </div>
          </div>
        );

      case 'company':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Company Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company-name">Company Name *</Label>
                  <Input
                    id="company-name"
                    value={formData.companyName}
                    onChange={(e) => updateFormData({ companyName: e.target.value })}
                    placeholder="Enter company name"
                  />
                </div>
                <div>
                  <Label htmlFor="reg-number">Registration Number *</Label>
                  <Input
                    id="reg-number"
                    value={formData.registrationNumber}
                    onChange={(e) => updateFormData({ registrationNumber: e.target.value })}
                    placeholder="e.g., 2023/123456/07"
                  />
                </div>
                <div>
                  <Label htmlFor="vat-number">VAT Number</Label>
                  <Input
                    id="vat-number"
                    value={formData.vatNumber}
                    onChange={(e) => updateFormData({ vatNumber: e.target.value })}
                    placeholder="e.g., 4123456789"
                  />
                </div>
                <div>
                  <Label htmlFor="bee-level">BEE Level</Label>
                  <Select value={formData.beeLevel} onValueChange={(value) => updateFormData({ beeLevel: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select BEE level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="level-1">Level 1</SelectItem>
                      <SelectItem value="level-2">Level 2</SelectItem>
                      <SelectItem value="level-3">Level 3</SelectItem>
                      <SelectItem value="level-4">Level 4</SelectItem>
                      <SelectItem value="level-5">Level 5</SelectItem>
                      <SelectItem value="level-6">Level 6</SelectItem>
                      <SelectItem value="level-7">Level 7</SelectItem>
                      <SelectItem value="level-8">Level 8</SelectItem>
                      <SelectItem value="non-compliant">Non-Compliant</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-medium mb-4">Contact Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contact-person">Contact Person *</Label>
                  <Input
                    id="contact-person"
                    value={formData.contactPerson}
                    onChange={(e) => updateFormData({ contactPerson: e.target.value })}
                    placeholder="Full name"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData({ email: e.target.value })}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => updateFormData({ phone: e.target.value })}
                    placeholder="+27 11 123 4567"
                  />
                </div>
              </div>
              <div className="mt-4">
                <Label htmlFor="address">Business Address</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => updateFormData({ address: e.target.value })}
                  placeholder="Full business address"
                  rows={3}
                />
              </div>
            </div>
          </div>
        );

      case 'technical':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Technical Proposal</h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="technical-approach">Technical Approach</Label>
                  <Textarea
                    id="technical-approach"
                    value={formData.technicalApproach}
                    onChange={(e) => updateFormData({ technicalApproach: e.target.value })}
                    placeholder="Describe your technical approach to this project..."
                    rows={4}
                  />
                </div>
                <div>
                  <Label htmlFor="methodology">Methodology</Label>
                  <Textarea
                    id="methodology"
                    value={formData.methodology}
                    onChange={(e) => updateFormData({ methodology: e.target.value })}
                    placeholder="Explain your methodology and processes..."
                    rows={4}
                  />
                </div>
                <div>
                  <Label htmlFor="timeline">Project Timeline</Label>
                  <Textarea
                    id="timeline"
                    value={formData.timeline}
                    onChange={(e) => updateFormData({ timeline: e.target.value })}
                    placeholder="Outline your project timeline and key milestones..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="deliverables">Key Deliverables</Label>
                  <Textarea
                    id="deliverables"
                    value={formData.deliverables}
                    onChange={(e) => updateFormData({ deliverables: e.target.value })}
                    placeholder="List the key deliverables and outcomes..."
                    rows={3}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'pricing':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Pricing Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="bid-amount">Bid Amount *</Label>
                  <div className="flex space-x-2">
                    <Select value={formData.currency} onValueChange={(value) => updateFormData({ currency: value })}>
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ZAR">ZAR</SelectItem>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                      </SelectContent>
                    </Select>
                    <Input
                      id="bid-amount"
                      type="number"
                      value={formData.bidAmount}
                      onChange={(e) => updateFormData({ bidAmount: parseFloat(e.target.value) || 0 })}
                      placeholder="0.00"
                      className="flex-1"
                    />
                  </div>
                  {tender.estimated_value && (
                    <p className="text-sm text-gray-600 mt-1">
                      Tender estimated value: {formatCurrency(tender.estimated_value)}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="validity-period">Validity Period (days)</Label>
                  <Input
                    id="validity-period"
                    type="number"
                    value={formData.validityPeriod}
                    onChange={(e) => updateFormData({ validityPeriod: parseInt(e.target.value) || 30 })}
                    placeholder="30"
                  />
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-4">Additional Pricing Information</h4>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="assumptions">Assumptions</Label>
                  <Textarea
                    id="assumptions"
                    value={formData.assumptions}
                    onChange={(e) => updateFormData({ assumptions: e.target.value })}
                    placeholder="List any assumptions made in your pricing..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="exclusions">Exclusions</Label>
                  <Textarea
                    id="exclusions"
                    value={formData.exclusions}
                    onChange={(e) => updateFormData({ exclusions: e.target.value })}
                    placeholder="List any exclusions from your bid..."
                    rows={3}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'team':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Team & Resources</h3>
              <div className="space-y-4">
                {formData.teamMembers.map((member, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label>Name</Label>
                          <Input
                            value={member.name}
                            onChange={(e) => {
                              const updated = [...formData.teamMembers];
                              updated[index].name = e.target.value;
                              updateFormData({ teamMembers: updated });
                            }}
                            placeholder="Full name"
                          />
                        </div>
                        <div>
                          <Label>Role</Label>
                          <Input
                            value={member.role}
                            onChange={(e) => {
                              const updated = [...formData.teamMembers];
                              updated[index].role = e.target.value;
                              updateFormData({ teamMembers: updated });
                            }}
                            placeholder="e.g., Project Manager"
                          />
                        </div>
                        <div>
                          <Label>Experience</Label>
                          <Input
                            value={member.experience}
                            onChange={(e) => {
                              const updated = [...formData.teamMembers];
                              updated[index].experience = e.target.value;
                              updateFormData({ teamMembers: updated });
                            }}
                            placeholder="Years of experience"
                          />
                        </div>
                      </div>
                      <div className="flex justify-end mt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const updated = formData.teamMembers.filter((_, i) => i !== index);
                            updateFormData({ teamMembers: updated });
                          }}
                        >
                          Remove
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                <Button
                  variant="outline"
                  onClick={() => {
                    const updated = [...formData.teamMembers, { name: '', role: '', experience: '' }];
                    updateFormData({ teamMembers: updated });
                  }}
                >
                  <Users className="w-4 h-4 mr-2" />
                  Add Team Member
                </Button>
              </div>
            </div>
          </div>
        );

      case 'compliance':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Compliance Checklist</h3>
              <p className="text-gray-600 mb-6">
                Please confirm compliance with the following requirements:
              </p>

              <div className="space-y-4">
                {formData.complianceItems.map((item, index) => (
                  <Card key={item.id} className={item.isCompliant ? 'border-green-200' : 'border-gray-200'}>
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          checked={item.isCompliant}
                          onCheckedChange={(checked) => {
                            const updated = [...formData.complianceItems];
                            updated[index].isCompliant = checked as boolean;
                            updateFormData({ complianceItems: updated });
                          }}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium">{item.requirement}</h4>
                          {item.isCompliant && (
                            <div className="mt-2">
                              <Label>Evidence/Notes</Label>
                              <Textarea
                                value={item.evidence || ''}
                                onChange={(e) => {
                                  const updated = [...formData.complianceItems];
                                  updated[index].evidence = e.target.value;
                                  updateFormData({ complianceItems: updated });
                                }}
                                placeholder="Provide evidence or notes for compliance..."
                                rows={2}
                              />
                            </div>
                          )}
                        </div>
                        {item.isCompliant && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            Compliant
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Items marked with * are typically required for bid submission.
                  Ensure you have the necessary documentation ready.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        );

      case 'review':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Review Your Bid</h3>
              <p className="text-gray-600 mb-6">
                Please review all information before submitting your bid.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Company Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <p className="text-sm text-gray-600">Company</p>
                    <p className="font-medium">{formData.companyName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Contact Person</p>
                    <p className="font-medium">{formData.contactPerson}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Email</p>
                    <p className="font-medium">{formData.email}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Bid Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <p className="text-sm text-gray-600">Bid Amount</p>
                    <p className="font-medium text-lg">{formatCurrency(formData.bidAmount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Validity Period</p>
                    <p className="font-medium">{formData.validityPeriod} days</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Team Members</p>
                    <p className="font-medium">{formData.teamMembers.length}</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Compliance Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {formData.complianceItems.map((item) => (
                    <div key={item.id} className="flex items-center justify-between">
                      <span className="text-sm">{item.requirement}</span>
                      <Badge className={item.isCompliant ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {item.isCompliant ? 'Compliant' : 'Not Compliant'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                By submitting this bid, you confirm that all information provided is accurate
                and that you have the authority to submit this bid on behalf of your organization.
              </AlertDescription>
            </Alert>
          </div>
        );

      default:
        return <div>Step content for {stepId}</div>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Bid Creation Wizard</h1>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>

        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Step {currentStep + 1} of {WIZARD_STEPS.length}</span>
            <span>{Math.round(getStepProgress())}% Complete</span>
          </div>
          <Progress value={getStepProgress()} />
        </div>

        {/* Step Navigation */}
        <div className="flex items-center space-x-2 overflow-x-auto pb-2">
          {WIZARD_STEPS.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;

            return (
              <div
                key={step.id}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg whitespace-nowrap ${
                  isActive ? 'bg-blue-100 text-blue-700' :
                  isCompleted ? 'bg-green-100 text-green-700' :
                  'bg-gray-100 text-gray-600'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{step.title}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="mb-8"
        >
          {renderStepContent()}
        </motion.div>
      </AnimatePresence>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 0}
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Previous
        </Button>

        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={saveDraft}
            disabled={loading}
          >
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </Button>

          {currentStep === WIZARD_STEPS.length - 1 ? (
            <Button
              onClick={submitBid}
              disabled={loading}
            >
              <Send className="w-4 h-4 mr-2" />
              Submit Bid
            </Button>
          ) : (
            <Button
              onClick={nextStep}
              disabled={validationErrors.length > 0}
            >
              Next
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default BidCreationWizard;