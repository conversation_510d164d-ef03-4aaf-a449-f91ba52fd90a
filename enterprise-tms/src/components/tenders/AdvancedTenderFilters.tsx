import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Filter, 
  Search, 
  Calendar, 
  DollarSign, 
  MapPin, 
  Building, 
  Target,
  Brain,
  Clock,
  Star,
  X,
  ChevronDown,
  ChevronUp,
  Save,
  RotateCcw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

export interface AdvancedFilterState {
  // Basic filters
  searchQuery: string;
  categories: string[];
  statuses: string[];
  
  // Value filters
  valueRange: [number, number];
  currency: string;
  
  // Date filters
  publishDateRange: {
    from: string;
    to: string;
  };
  closingDateRange: {
    from: string;
    to: string;
  };
  
  // Location filters
  location: string;
  radius: number; // km
  coordinates: {
    lat: number;
    lng: number;
  } | null;
  
  // AI & Scoring filters
  aiMatchScoreRange: [number, number];
  winProbabilityRange: [number, number];
  riskLevels: string[];
  
  // Requirement filters
  compulsoryBriefingOnly: boolean;
  performanceGuaranteeRequired: boolean;
  
  // Advanced filters
  issuerTypes: string[];
  tenderSources: string[];
  documentTypes: string[];
  
  // Sorting
  sortBy: string;
  sortDirection: 'asc' | 'desc';
  
  // Saved filters
  savedFilterName?: string;
}

interface AdvancedTenderFiltersProps {
  filters: AdvancedFilterState;
  onFiltersChange: (filters: AdvancedFilterState) => void;
  onSaveFilters?: (name: string, filters: AdvancedFilterState) => void;
  onLoadFilters?: (filters: AdvancedFilterState) => void;
  savedFilters?: Array<{ name: string; filters: AdvancedFilterState }>;
  className?: string;
}

const TENDER_CATEGORIES = [
  'construction',
  'it_services',
  'consulting',
  'supplies',
  'maintenance',
  'professional_services',
  'transport',
  'healthcare',
  'education',
  'security'
];

const TENDER_STATUSES = [
  'draft',
  'published',
  'open',
  'closed',
  'awarded',
  'cancelled'
];

const RISK_LEVELS = [
  'low',
  'medium',
  'high',
  'critical'
];

const ISSUER_TYPES = [
  'government',
  'municipality',
  'state_owned_enterprise',
  'private_company',
  'ngo',
  'international_organization'
];

const TENDER_SOURCES = [
  'etenders',
  'government_gazette',
  'company_website',
  'tender_bulletin',
  'manual_entry'
];

const SORT_OPTIONS = [
  { value: 'closing_date-asc', label: 'Closing Date (Earliest)' },
  { value: 'closing_date-desc', label: 'Closing Date (Latest)' },
  { value: 'estimated_value-desc', label: 'Value (Highest)' },
  { value: 'estimated_value-asc', label: 'Value (Lowest)' },
  { value: 'ai_match_score-desc', label: 'AI Match Score (Highest)' },
  { value: 'ai_win_probability-desc', label: 'Win Probability (Highest)' },
  { value: 'created_at-desc', label: 'Recently Added' },
  { value: 'title-asc', label: 'Title (A-Z)' }
];

const AdvancedTenderFilters: React.FC<AdvancedTenderFiltersProps> = ({
  filters,
  onFiltersChange,
  onSaveFilters,
  onLoadFilters,
  savedFilters = [],
  className = ''
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic']));
  const [saveFilterName, setSaveFilterName] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const updateFilters = (updates: Partial<AdvancedFilterState>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const resetFilters = () => {
    const defaultFilters: AdvancedFilterState = {
      searchQuery: '',
      categories: [],
      statuses: ['open', 'published'],
      valueRange: [0, 100000000],
      currency: 'ZAR',
      publishDateRange: { from: '', to: '' },
      closingDateRange: { from: '', to: '' },
      location: '',
      radius: 50,
      coordinates: null,
      aiMatchScoreRange: [0, 100],
      winProbabilityRange: [0, 100],
      riskLevels: [],
      compulsoryBriefingOnly: false,
      performanceGuaranteeRequired: false,
      issuerTypes: [],
      tenderSources: [],
      documentTypes: [],
      sortBy: 'closing_date',
      sortDirection: 'asc'
    };
    onFiltersChange(defaultFilters);
  };

  const handleSaveFilters = () => {
    if (saveFilterName.trim() && onSaveFilters) {
      onSaveFilters(saveFilterName.trim(), filters);
      setSaveFilterName('');
      setShowSaveDialog(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: filters.currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.searchQuery) count++;
    if (filters.categories.length > 0) count++;
    if (filters.statuses.length !== 2) count++; // Default is 2 statuses
    if (filters.valueRange[0] > 0 || filters.valueRange[1] < 100000000) count++;
    if (filters.publishDateRange.from || filters.publishDateRange.to) count++;
    if (filters.closingDateRange.from || filters.closingDateRange.to) count++;
    if (filters.location) count++;
    if (filters.aiMatchScoreRange[0] > 0 || filters.aiMatchScoreRange[1] < 100) count++;
    if (filters.winProbabilityRange[0] > 0 || filters.winProbabilityRange[1] < 100) count++;
    if (filters.riskLevels.length > 0) count++;
    if (filters.compulsoryBriefingOnly) count++;
    if (filters.performanceGuaranteeRequired) count++;
    if (filters.issuerTypes.length > 0) count++;
    if (filters.tenderSources.length > 0) count++;
    return count;
  };

  const FilterSection: React.FC<{
    id: string;
    title: string;
    icon: React.ReactNode;
    children: React.ReactNode;
  }> = ({ id, title, icon, children }) => {
    const isExpanded = expandedSections.has(id);
    
    return (
      <Collapsible open={isExpanded} onOpenChange={() => toggleSection(id)}>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-3 h-auto">
            <div className="flex items-center space-x-2">
              {icon}
              <span className="font-medium">{title}</span>
            </div>
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="p-4 pt-0">
            {children}
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5" />
          <h3 className="text-lg font-semibold">Advanced Filters</h3>
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary">
              {getActiveFilterCount()} active
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={resetFilters}>
            <RotateCcw className="w-4 h-4 mr-1" />
            Reset
          </Button>
          {onSaveFilters && (
            <Button variant="outline" size="sm" onClick={() => setShowSaveDialog(true)}>
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
          )}
        </div>
      </div>

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <div>
          <Label className="text-sm font-medium">Saved Filters</Label>
          <div className="flex flex-wrap gap-2 mt-2">
            {savedFilters.map((saved, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onLoadFilters?.(saved.filters)}
              >
                {saved.name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Basic Filters */}
      <Card>
        <FilterSection
          id="basic"
          title="Basic Filters"
          icon={<Search className="w-4 h-4" />}
        >
          <div className="space-y-4">
            {/* Search */}
            <div>
              <Label>Search Query</Label>
              <Input
                placeholder="Search by title, description, issuer..."
                value={filters.searchQuery}
                onChange={(e) => updateFilters({ searchQuery: e.target.value })}
              />
            </div>

            {/* Categories */}
            <div>
              <Label>Categories</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {TENDER_CATEGORIES.map(category => (
                  <div key={category} className="flex items-center space-x-2">
                    <Checkbox
                      id={category}
                      checked={filters.categories.includes(category)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({
                            categories: [...filters.categories, category]
                          });
                        } else {
                          updateFilters({
                            categories: filters.categories.filter(c => c !== category)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={category} className="text-sm capitalize">
                      {category.replace('_', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Status */}
            <div>
              <Label>Status</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {TENDER_STATUSES.map(status => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={status}
                      checked={filters.statuses.includes(status)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({
                            statuses: [...filters.statuses, status]
                          });
                        } else {
                          updateFilters({
                            statuses: filters.statuses.filter(s => s !== status)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={status} className="text-sm capitalize">
                      {status}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Sort */}
            <div>
              <Label>Sort By</Label>
              <Select
                value={`${filters.sortBy}-${filters.sortDirection}`}
                onValueChange={(value) => {
                  const [sortBy, sortDirection] = value.split('-');
                  updateFilters({ sortBy, sortDirection: sortDirection as 'asc' | 'desc' });
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SORT_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </FilterSection>
      </Card>

      {/* Value & Date Filters */}
      <Card>
        <FilterSection
          id="value-date"
          title="Value & Date Filters"
          icon={<DollarSign className="w-4 h-4" />}
        >
          <div className="space-y-4">
            {/* Value Range */}
            <div>
              <Label>
                Value Range: {formatCurrency(filters.valueRange[0])} - {formatCurrency(filters.valueRange[1])}
              </Label>
              <Slider
                value={filters.valueRange}
                onValueChange={(value) => updateFilters({ valueRange: value as [number, number] })}
                max={100000000}
                step={100000}
                className="mt-2"
              />
            </div>

            {/* Closing Date Range */}
            <div>
              <Label>Closing Date Range</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <Input
                  type="date"
                  value={filters.closingDateRange.from}
                  onChange={(e) => updateFilters({
                    closingDateRange: { ...filters.closingDateRange, from: e.target.value }
                  })}
                />
                <Input
                  type="date"
                  value={filters.closingDateRange.to}
                  onChange={(e) => updateFilters({
                    closingDateRange: { ...filters.closingDateRange, to: e.target.value }
                  })}
                />
              </div>
            </div>

            {/* Publish Date Range */}
            <div>
              <Label>Publish Date Range</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <Input
                  type="date"
                  value={filters.publishDateRange.from}
                  onChange={(e) => updateFilters({
                    publishDateRange: { ...filters.publishDateRange, from: e.target.value }
                  })}
                />
                <Input
                  type="date"
                  value={filters.publishDateRange.to}
                  onChange={(e) => updateFilters({
                    publishDateRange: { ...filters.publishDateRange, to: e.target.value }
                  })}
                />
              </div>
            </div>
          </div>
        </FilterSection>
      </Card>

      {/* Location Filters */}
      <Card>
        <FilterSection
          id="location"
          title="Location Filters"
          icon={<MapPin className="w-4 h-4" />}
        >
          <div className="space-y-4">
            <div>
              <Label>Location</Label>
              <Input
                placeholder="City, province, or region..."
                value={filters.location}
                onChange={(e) => updateFilters({ location: e.target.value })}
              />
            </div>
            <div>
              <Label>Search Radius: {filters.radius} km</Label>
              <Slider
                value={[filters.radius]}
                onValueChange={(value) => updateFilters({ radius: value[0] })}
                max={500}
                step={10}
                className="mt-2"
              />
            </div>
          </div>
        </FilterSection>
      </Card>

      {/* AI & Scoring Filters */}
      <Card>
        <FilterSection
          id="ai-scoring"
          title="AI & Scoring Filters"
          icon={<Brain className="w-4 h-4" />}
        >
          <div className="space-y-4">
            <div>
              <Label>
                AI Match Score: {filters.aiMatchScoreRange[0]}% - {filters.aiMatchScoreRange[1]}%
              </Label>
              <Slider
                value={filters.aiMatchScoreRange}
                onValueChange={(value) => updateFilters({ aiMatchScoreRange: value as [number, number] })}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
            <div>
              <Label>
                Win Probability: {filters.winProbabilityRange[0]}% - {filters.winProbabilityRange[1]}%
              </Label>
              <Slider
                value={filters.winProbabilityRange}
                onValueChange={(value) => updateFilters({ winProbabilityRange: value as [number, number] })}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
            <div>
              <Label>Risk Levels</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {RISK_LEVELS.map(level => (
                  <div key={level} className="flex items-center space-x-2">
                    <Checkbox
                      id={level}
                      checked={filters.riskLevels.includes(level)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({
                            riskLevels: [...filters.riskLevels, level]
                          });
                        } else {
                          updateFilters({
                            riskLevels: filters.riskLevels.filter(r => r !== level)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={level} className="text-sm capitalize">
                      {level}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </FilterSection>
      </Card>

      {/* Requirements */}
      <Card>
        <FilterSection
          id="requirements"
          title="Requirements"
          icon={<Target className="w-4 h-4" />}
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="compulsory-briefing"
                checked={filters.compulsoryBriefingOnly}
                onCheckedChange={(checked) => updateFilters({ compulsoryBriefingOnly: checked })}
              />
              <Label htmlFor="compulsory-briefing">Compulsory briefing only</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="performance-guarantee"
                checked={filters.performanceGuaranteeRequired}
                onCheckedChange={(checked) => updateFilters({ performanceGuaranteeRequired: checked })}
              />
              <Label htmlFor="performance-guarantee">Performance guarantee required</Label>
            </div>
          </div>
        </FilterSection>
      </Card>

      {/* Save Filter Dialog */}
      <AnimatePresence>
        {showSaveDialog && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-lg p-6 w-96"
            >
              <h3 className="text-lg font-semibold mb-4">Save Filter Set</h3>
              <Input
                placeholder="Enter filter name..."
                value={saveFilterName}
                onChange={(e) => setSaveFilterName(e.target.value)}
                className="mb-4"
              />
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveFilters} disabled={!saveFilterName.trim()}>
                  Save
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdvancedTenderFilters;
