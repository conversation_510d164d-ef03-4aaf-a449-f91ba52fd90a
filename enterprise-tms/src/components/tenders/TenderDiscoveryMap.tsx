import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MapPin,
  Filter,
  Search,
  Calendar,
  DollarSign,
  Building,
  Target,
  Eye,
  Plus,
  X,
  ChevronDown,
  Layers,
  RefreshCw
} from 'lucide-react';
import L from 'leaflet';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Sheet, SheetContent, SheetD<PERSON><PERSON>, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet';
import { tenderService } from '../../services/api';
import type { TenderData } from '../../lib/supabase';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface TenderDiscoveryMapProps {
  onTenderSelect?: (tender: TenderData) => void;
  selectedTenders?: string[];
  onTenderToggle?: (tenderId: string) => void;
}

interface FilterState {
  searchQuery: string;
  categories: string[];
  statuses: string[];
  valueRange: [number, number];
  dateRange: {
    from: string;
    to: string;
  };
  location: string;
}

const TENDER_CATEGORIES = [
  'services',
  'goods',
  'works',
  'construction',
  'it_services',
  'consulting',
  'supplies',
  'maintenance'
];

const TENDER_STATUSES = [
  'open',
  'closed',
  'awarded',
  'cancelled',
  'advertised',
  'quotation'
];

const TenderDiscoveryMap: React.FC<TenderDiscoveryMapProps> = ({
  onTenderSelect,
  selectedTenders = [],
  onTenderToggle
}) => {
  const [tenders, setTenders] = useState<TenderData[]>([]);
  const [filteredTenders, setFilteredTenders] = useState<TenderData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTender, setSelectedTender] = useState<TenderData | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([-26.2041, 28.0473]); // Johannesburg
  const [mapZoom, setMapZoom] = useState(6);
  const [showFilters, setShowFilters] = useState(false);
  const [totalTenderCount, setTotalTenderCount] = useState<number>(0);
  
  const [filters, setFilters] = useState<FilterState>({
    searchQuery: '',
    categories: [],
    statuses: ['open', 'advertised', 'quotation'], // Updated to match database statuses
    valueRange: [0, 100000000],
    dateRange: {
      from: '',
      to: ''
    },
    location: ''
  });

  useEffect(() => {
    loadTenders();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [tenders, filters]);

  const loadTenders = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // Load all tenders without limit, with basic filtering
      const data = await tenderService.getTendersByLocation(undefined, {
        status: filters.statuses,
        limit: undefined // No limit - show all tenders
      });
      setTenders(data);

      // Also get total count for display
      const allTenders = await tenderService.getAllTenders();
      setTotalTenderCount(allTenders.length);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load tenders');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadTenders(true);
  };

  const applyFilters = () => {
    let filtered = [...tenders];

    // Search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(tender =>
        tender.title.toLowerCase().includes(query) ||
        tender.description?.toLowerCase().includes(query) ||
        tender.issuer?.toLowerCase().includes(query)
      );
    }

    // Categories
    if (filters.categories.length > 0) {
      filtered = filtered.filter(tender =>
        filters.categories.includes(tender.category || '')
      );
    }

    // Statuses
    if (filters.statuses.length > 0) {
      filtered = filtered.filter(tender =>
        filters.statuses.includes(tender.status)
      );
    }

    // Value range
    filtered = filtered.filter(tender => {
      const value = tender.estimated_value || 0;
      return value >= filters.valueRange[0] && value <= filters.valueRange[1];
    });

    // Date range
    if (filters.dateRange.from) {
      filtered = filtered.filter(tender =>
        !tender.closing_date || tender.closing_date >= filters.dateRange.from
      );
    }
    if (filters.dateRange.to) {
      filtered = filtered.filter(tender =>
        !tender.closing_date || tender.closing_date <= filters.dateRange.to
      );
    }

    // Location
    if (filters.location) {
      const location = filters.location.toLowerCase();
      filtered = filtered.filter(tender =>
        tender.location?.toLowerCase().includes(location)
      );
    }

    setFilteredTenders(filtered);
  };

  const handleTenderClick = (tender: TenderData) => {
    setSelectedTender(tender);
    if (onTenderSelect) {
      onTenderSelect(tender);
    }
  };

  const handleTenderToggle = (tender: TenderData) => {
    if (onTenderToggle) {
      onTenderToggle(tender.id);
    }
  };

  const createCustomIcon = (tender: TenderData) => {
    const isSelected = selectedTenders.includes(tender.id);
    const color = isSelected ? '#10b981' : '#3b82f6';
    
    return L.divIcon({
      html: `
        <div style="
          background-color: ${color};
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 12px;
        ">
          ${isSelected ? '✓' : '₹'}
        </div>
      `,
      className: 'custom-tender-marker',
      iconSize: [30, 30],
      iconAnchor: [15, 15]
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const FilterPanel = () => (
    <div className="space-y-6">
      {/* Search */}
      <div>
        <label className="text-sm font-medium mb-2 block">Search Tenders</label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search by title, description, or issuer..."
            value={filters.searchQuery}
            onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
            className="pl-10"
          />
        </div>
      </div>

      {/* Categories */}
      <div>
        <label className="text-sm font-medium mb-2 block">Categories</label>
        <div className="space-y-2">
          {TENDER_CATEGORIES.map(category => (
            <div key={category} className="flex items-center space-x-2">
              <Checkbox
                id={category}
                checked={filters.categories.includes(category)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setFilters(prev => ({
                      ...prev,
                      categories: [...prev.categories, category]
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      categories: prev.categories.filter(c => c !== category)
                    }));
                  }
                }}
              />
              <label htmlFor={category} className="text-sm capitalize">
                {category.replace('_', ' ')}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Status */}
      <div>
        <label className="text-sm font-medium mb-2 block">Tender Status</label>
        <div className="space-y-2">
          {TENDER_STATUSES.map(status => (
            <div key={status} className="flex items-center space-x-2">
              <Checkbox
                id={status}
                checked={filters.statuses.includes(status)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setFilters(prev => ({
                      ...prev,
                      statuses: [...prev.statuses, status]
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      statuses: prev.statuses.filter(s => s !== status)
                    }));
                  }
                }}
              />
              <label htmlFor={status} className="text-sm capitalize">
                {status.replace('_', ' ')}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Value Range */}
      <div>
        <label className="text-sm font-medium mb-2 block">
          Value Range: {formatCurrency(filters.valueRange[0])} - {formatCurrency(filters.valueRange[1])}
        </label>
        <Slider
          value={filters.valueRange}
          onValueChange={(value) => setFilters(prev => ({ ...prev, valueRange: value as [number, number] }))}
          max={100000000}
          step={100000}
          className="w-full"
        />
      </div>

      {/* Date Range */}
      <div>
        <label className="text-sm font-medium mb-2 block">Closing Date Range</label>
        <div className="grid grid-cols-2 gap-2">
          <Input
            type="date"
            value={filters.dateRange.from}
            onChange={(e) => setFilters(prev => ({
              ...prev,
              dateRange: { ...prev.dateRange, from: e.target.value }
            }))}
          />
          <Input
            type="date"
            value={filters.dateRange.to}
            onChange={(e) => setFilters(prev => ({
              ...prev,
              dateRange: { ...prev.dateRange, to: e.target.value }
            }))}
          />
        </div>
      </div>

      {/* Location */}
      <div>
        <label className="text-sm font-medium mb-2 block">Location</label>
        <Input
          placeholder="Filter by location..."
          value={filters.location}
          onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
        />
      </div>

      {/* Clear Filters */}
      <Button
        variant="outline"
        onClick={() => setFilters({
          searchQuery: '',
          categories: [],
          statuses: ['open', 'advertised', 'quotation'],
          valueRange: [0, 100000000],
          dateRange: { from: '', to: '' },
          location: ''
        })}
        className="w-full"
      >
        Clear All Filters
      </Button>
    </div>
  );

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading tender map...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadTenders}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b p-4 flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Tender Discovery Map</h2>
          <p className="text-sm text-gray-600">
            Showing {filteredTenders.length} of {tenders.length} loaded tenders
            {totalTenderCount > 0 && (
              <span className="ml-1">
                (Total in database: {totalTenderCount.toLocaleString()})
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Sheet open={showFilters} onOpenChange={setShowFilters}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filters
                {(filters.categories.length > 0 || filters.searchQuery || filters.location) && (
                  <Badge variant="secondary" className="ml-2">
                    {filters.categories.length + (filters.searchQuery ? 1 : 0) + (filters.location ? 1 : 0)}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Tenders</SheetTitle>
                <SheetDescription>
                  Refine your tender search with advanced filters
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6">
                <FilterPanel />
              </div>
            </SheetContent>
          </Sheet>
          <Button onClick={loadTenders} variant="outline" size="sm">
            <Layers className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Map Container - Temporarily replaced with data debug view */}
      <div className="flex-1 relative bg-gray-50 p-4">
        {loading && (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p>Loading tenders...</p>
            </div>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-red-600">
              <p className="font-semibold">Error loading tenders:</p>
              <p className="text-sm">{error}</p>
              <Button onClick={loadTenders} className="mt-2">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        )}

        {!loading && !error && (
          <div className="h-full overflow-auto">
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Tender Data Debug</h3>
              <p className="text-sm text-gray-600">
                Total tenders: {tenders.length} | Filtered: {filteredTenders.length}
              </p>
            </div>

            {filteredTenders.length === 0 ? (
              <div className="text-center py-8">
                <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">No tenders found matching your criteria</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTenders.slice(0, 12).map((tender) => (
                  <div key={tender.id} className="bg-white p-4 rounded-lg shadow border">
                    <h4 className="font-semibold text-sm mb-2 line-clamp-2">{tender.title}</h4>
                    <div className="space-y-1 text-xs text-gray-600">
                      <p><strong>Issuer:</strong> {tender.issuer}</p>
                      <p><strong>Value:</strong> {formatCurrency(tender.estimated_value || 0)}</p>
                      <p><strong>Location:</strong> {tender.location}</p>
                      <p><strong>Coordinates:</strong> {tender.latitude}, {tender.longitude}</p>
                    </div>
                    <div className="flex items-center justify-between mt-3">
                      <Badge variant={tender.status === 'open' ? 'default' : 'secondary'}>
                        {tender.status}
                      </Badge>
                      <Button
                        size="sm"
                        variant={selectedTenders.includes(tender.id) ? "default" : "outline"}
                        onClick={() => handleTenderToggle(tender)}
                      >
                        {selectedTenders.includes(tender.id) ? 'Selected' : 'Select'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Selected Tender Details */}
        <AnimatePresence>
          {selectedTender && (
            <motion.div
              initial={{ x: 400 }}
              animate={{ x: 0 }}
              exit={{ x: 400 }}
              className="absolute top-4 right-4 w-80 bg-white rounded-lg shadow-lg border z-10"
            >
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{selectedTender.title}</CardTitle>
                      <CardDescription>{selectedTender.issuer}</CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedTender(null)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Value</p>
                      <p className="font-semibold">{formatCurrency(selectedTender.estimated_value || 0)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Closing Date</p>
                      <p className="font-semibold">
                        {selectedTender.closing_date ? new Date(selectedTender.closing_date).toLocaleDateString() : 'TBD'}
                      </p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-gray-600 text-sm">Description</p>
                    <p className="text-sm">{selectedTender.description?.substring(0, 150)}...</p>
                  </div>

                  <div className="flex items-center justify-between pt-2">
                    <Badge variant={selectedTender.status === 'open' ? 'default' : 'secondary'}>
                      {selectedTender.status}
                    </Badge>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleTenderToggle(selectedTender)}
                      >
                        {selectedTenders.includes(selectedTender.id) ? 'Remove' : 'Select'}
                      </Button>
                      <Button size="sm">
                        <Target className="w-3 h-3 mr-1" />
                        Start Bid
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default TenderDiscoveryMap;
