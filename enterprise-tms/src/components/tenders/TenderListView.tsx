import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  DollarSign, 
  MapPin, 
  Building, 
  Clock, 
  Target,
  Eye,
  Plus,
  X,
  Filter,
  Search,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { tenderService } from '../../services/api';
import type { TenderData } from '../../lib/supabase';

interface TenderListViewProps {
  onTenderSelect?: (tender: TenderData) => void;
  selectedTenders?: string[];
  onTenderToggle?: (tenderId: string) => void;
  searchQuery?: string;
  filters?: any;
}

type SortField = 'closing_date' | 'estimated_value' | 'created_at' | 'title';
type SortDirection = 'asc' | 'desc';

const TenderListView: React.FC<TenderListViewProps> = ({
  onTenderSelect,
  selectedTenders = [],
  onTenderToggle,
  searchQuery = '',
  filters = {}
}) => {
  const [tenders, setTenders] = useState<TenderData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortField, setSortField] = useState<SortField>('closing_date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  useEffect(() => {
    loadTenders();
  }, []);

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  const loadTenders = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await tenderService.getAllTenders(filters);
      setTenders(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load tenders');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedAndFilteredTenders = React.useMemo(() => {
    let filtered = [...tenders];

    // Apply search filter
    if (localSearchQuery) {
      const query = localSearchQuery.toLowerCase();
      filtered = filtered.filter(tender =>
        tender.title.toLowerCase().includes(query) ||
        tender.description?.toLowerCase().includes(query) ||
        tender.issuer?.toLowerCase().includes(query) ||
        tender.location?.toLowerCase().includes(query)
      );
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === 'estimated_value') {
        aValue = aValue || 0;
        bValue = bValue || 0;
      }

      if (sortField === 'closing_date' || sortField === 'created_at') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      }

      if (sortField === 'title') {
        aValue = aValue?.toLowerCase() || '';
        bValue = bValue?.toLowerCase() || '';
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [tenders, localSearchQuery, sortField, sortDirection]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysUntilClosing = (closingDate: string) => {
    const today = new Date();
    const closing = new Date(closingDate);
    const diffTime = closing.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'published':
        return 'bg-blue-100 text-blue-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      case 'awarded':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (daysUntilClosing: number) => {
    if (daysUntilClosing < 0) return 'text-red-600';
    if (daysUntilClosing <= 7) return 'text-orange-600';
    if (daysUntilClosing <= 30) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading tenders...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadTenders}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with Search and Sort */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search tenders..."
              value={localSearchQuery}
              onChange={(e) => setLocalSearchQuery(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
            const [field, direction] = value.split('-') as [SortField, SortDirection];
            setSortField(field);
            setSortDirection(direction);
          }}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="closing_date-asc">Closing Date (Earliest)</SelectItem>
              <SelectItem value="closing_date-desc">Closing Date (Latest)</SelectItem>
              <SelectItem value="estimated_value-desc">Value (Highest)</SelectItem>
              <SelectItem value="estimated_value-asc">Value (Lowest)</SelectItem>
              <SelectItem value="created_at-desc">Recently Added</SelectItem>
              <SelectItem value="title-asc">Title (A-Z)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="text-sm text-gray-600">
          {sortedAndFilteredTenders.length} tender{sortedAndFilteredTenders.length !== 1 ? 's' : ''} found
        </div>
      </div>

      {/* Tender List */}
      <div className="space-y-4">
        {sortedAndFilteredTenders.map((tender, index) => {
          const isSelected = selectedTenders.includes(tender.id);
          const daysUntilClosing = tender.closing_date ? getDaysUntilClosing(tender.closing_date) : null;
          
          return (
            <motion.div
              key={tender.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <Card className={`hover:shadow-md transition-shadow cursor-pointer ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <CardTitle className="text-lg hover:text-blue-600 transition-colors">
                          {tender.title}
                        </CardTitle>
                        <Badge className={getStatusColor(tender.status)}>
                          {tender.status}
                        </Badge>
                        {tender.ai_match_score && tender.ai_match_score > 80 && (
                          <Badge variant="secondary">
                            {tender.ai_match_score}% Match
                          </Badge>
                        )}
                      </div>
                      <CardDescription className="flex items-center space-x-4">
                        <span className="flex items-center">
                          <Building className="w-4 h-4 mr-1" />
                          {tender.issuer}
                        </span>
                        {tender.location && (
                          <span className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            {tender.location}
                          </span>
                        )}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => onTenderToggle?.(tender.id)}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <div>
                        <p className="text-sm text-gray-600">Estimated Value</p>
                        <p className="font-semibold">
                          {tender.estimated_value ? formatCurrency(tender.estimated_value) : 'Not specified'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-blue-600" />
                      <div>
                        <p className="text-sm text-gray-600">Closing Date</p>
                        <p className="font-semibold">
                          {tender.closing_date ? formatDate(tender.closing_date) : 'TBD'}
                        </p>
                      </div>
                    </div>
                    {daysUntilClosing !== null && (
                      <div className="flex items-center space-x-2">
                        <Clock className={`w-4 h-4 ${getUrgencyColor(daysUntilClosing)}`} />
                        <div>
                          <p className="text-sm text-gray-600">Time Remaining</p>
                          <p className={`font-semibold ${getUrgencyColor(daysUntilClosing)}`}>
                            {daysUntilClosing < 0 
                              ? 'Closed' 
                              : daysUntilClosing === 0 
                                ? 'Closes today' 
                                : `${daysUntilClosing} day${daysUntilClosing !== 1 ? 's' : ''}`
                            }
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {tender.description && (
                    <p className="text-sm text-gray-700 mb-4 line-clamp-2">
                      {tender.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {tender.category && (
                        <Badge variant="outline" className="capitalize">
                          {tender.category.replace('_', ' ')}
                        </Badge>
                      )}
                      {tender.compulsory_briefing && (
                        <Badge variant="destructive">
                          Compulsory Briefing
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onTenderSelect?.(tender)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View Details
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => onTenderSelect?.(tender)}
                        disabled={tender.status === 'closed' || (daysUntilClosing !== null && daysUntilClosing < 0)}
                      >
                        <Target className="w-4 h-4 mr-1" />
                        Start Bid
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {sortedAndFilteredTenders.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No tenders found</h3>
          <p className="text-gray-600">
            {localSearchQuery 
              ? `No tenders match your search for "${localSearchQuery}"`
              : 'No tenders available with the current filters'
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default TenderListView;
