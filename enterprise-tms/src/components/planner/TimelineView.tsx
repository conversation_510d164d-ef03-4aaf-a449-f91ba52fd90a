import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  Clock, 
  Users, 
  CheckSquare, 
  AlertTriangle,
  MapPin,
  Building,
  Target,
  User,
  Timer,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import type { TenderMeeting, TenderTask } from '../../lib/supabase';

interface TimelineViewProps {
  meetings: TenderMeeting[];
  tasks: TenderTask[];
  onTaskComplete?: (taskId: string) => void;
  onTaskEdit?: (task: TenderTask) => void;
  onMeetingView?: (meeting: TenderMeeting) => void;
}

interface TimelineItem {
  id: string;
  type: 'task' | 'meeting';
  date: string;
  title: string;
  description?: string;
  status?: string;
  priority?: string;
  data: TenderTask | TenderMeeting;
}

const TimelineView: React.FC<TimelineViewProps> = ({
  meetings,
  tasks,
  onTaskComplete,
  onTaskEdit,
  onMeetingView
}) => {
  const [currentWeek, setCurrentWeek] = useState(0);
  const [viewMode, setViewMode] = useState<'week' | 'month'>('week');

  // Combine and sort timeline items
  const timelineItems = useMemo(() => {
    const items: TimelineItem[] = [];

    // Add tasks
    tasks.forEach(task => {
      items.push({
        id: task.id,
        type: 'task',
        date: task.due_date,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        data: task
      });
    });

    // Add meetings
    meetings.forEach(meeting => {
      items.push({
        id: meeting.id,
        type: 'meeting',
        date: meeting.meeting_date,
        title: meeting.title,
        description: meeting.description,
        data: meeting
      });
    });

    // Sort by date
    return items.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [meetings, tasks]);

  // Group items by date
  const groupedItems = useMemo(() => {
    const groups: { [key: string]: TimelineItem[] } = {};
    
    timelineItems.forEach(item => {
      const dateKey = new Date(item.date).toISOString().split('T')[0];
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(item);
    });

    return groups;
  }, [timelineItems]);

  // Get current week dates
  const getCurrentWeekDates = () => {
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay() + (currentWeek * 7));
    
    const dates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      dates.push(date.toISOString().split('T')[0]);
    }
    return dates;
  };

  const weekDates = getCurrentWeekDates();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      day: date.getDate(),
      month: date.toLocaleDateString('en-ZA', { month: 'short' }),
      weekday: date.toLocaleDateString('en-ZA', { weekday: 'short' }),
      full: date.toLocaleDateString('en-ZA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };
  };

  const formatTime = (dateString: string, timeString?: string) => {
    if (timeString) {
      return new Date(`${dateString}T${timeString}`).toLocaleTimeString('en-ZA', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }
    return '';
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      default:
        return 'bg-green-500';
    }
  };

  const isToday = (dateString: string) => {
    const today = new Date().toISOString().split('T')[0];
    return dateString === today;
  };

  const isPast = (dateString: string) => {
    const today = new Date().toISOString().split('T')[0];
    return dateString < today;
  };

  const TaskItem: React.FC<{ item: TimelineItem }> = ({ item }) => {
    const task = item.data as TenderTask;
    const isOverdue = isPast(item.date) && task.status !== 'completed';
    
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className={`p-3 rounded-lg border-l-4 ${
          isOverdue ? 'border-l-red-500 bg-red-50' : 
          task.priority === 'critical' ? 'border-l-red-500 bg-white' :
          task.priority === 'high' ? 'border-l-orange-500 bg-white' :
          'border-l-blue-500 bg-white'
        } shadow-sm hover:shadow-md transition-shadow`}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <Checkbox
              checked={task.status === 'completed'}
              onCheckedChange={() => onTaskComplete?.(task.id)}
              className="mt-1"
            />
            <div className="flex-1">
              <h4 className="font-medium text-sm">{item.title}</h4>
              {item.description && (
                <p className="text-xs text-gray-600 mt-1">{item.description}</p>
              )}
              <div className="flex items-center space-x-3 mt-2">
                {task.assigned_to_role && (
                  <div className="flex items-center space-x-1">
                    <User className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600 capitalize">
                      {task.assigned_to_role.replace('_', ' ')}
                    </span>
                  </div>
                )}
                {task.estimated_hours && (
                  <div className="flex items-center space-x-1">
                    <Timer className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600">{task.estimated_hours}h</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`}></div>
            <Badge className={getTaskStatusColor(task.status)} variant="secondary">
              {task.status.replace('_', ' ')}
            </Badge>
          </div>
        </div>
        
        {task.checklist_items && task.checklist_items.length > 0 && (
          <div className="mt-3">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>Progress</span>
              <span>
                {task.checklist_items.filter(item => item.is_completed).length} / {task.checklist_items.length}
              </span>
            </div>
            <Progress 
              value={(task.checklist_items.filter(item => item.is_completed).length / task.checklist_items.length) * 100}
              className="h-1"
            />
          </div>
        )}
      </motion.div>
    );
  };

  const MeetingItem: React.FC<{ item: TimelineItem }> = ({ item }) => {
    const meeting = item.data as TenderMeeting;
    
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="p-3 rounded-lg border-l-4 border-l-purple-500 bg-purple-50 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        onClick={() => onMeetingView?.(meeting)}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <Users className="w-4 h-4 text-purple-600 mt-1" />
            <div className="flex-1">
              <h4 className="font-medium text-sm">{item.title}</h4>
              {item.description && (
                <p className="text-xs text-gray-600 mt-1">{item.description}</p>
              )}
              <div className="flex items-center space-x-3 mt-2">
                {meeting.meeting_time && (
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600">
                      {formatTime(meeting.meeting_date, meeting.meeting_time)}
                    </span>
                  </div>
                )}
                {meeting.location && (
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600">{meeting.location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {meeting.is_compulsory && (
              <Badge variant="destructive" className="text-xs">Required</Badge>
            )}
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              {meeting.meeting_type.replace('_', ' ')}
            </Badge>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Timeline Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentWeek(currentWeek - 1)}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <h3 className="text-lg font-semibold">
            {formatDate(weekDates[0]).full} - {formatDate(weekDates[6]).full}
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentWeek(currentWeek + 1)}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentWeek(0)}
        >
          Today
        </Button>
      </div>

      {/* Timeline Grid */}
      <div className="flex-1 overflow-auto">
        <div className="grid grid-cols-7 gap-4 h-full">
          {weekDates.map((date, index) => {
            const dateInfo = formatDate(date);
            const dayItems = groupedItems[date] || [];
            const todayClass = isToday(date) ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200';
            
            return (
              <div key={date} className={`border rounded-lg p-3 ${todayClass}`}>
                {/* Day Header */}
                <div className="text-center mb-3">
                  <div className="text-xs text-gray-600 uppercase tracking-wide">
                    {dateInfo.weekday}
                  </div>
                  <div className={`text-lg font-semibold ${isToday(date) ? 'text-blue-600' : 'text-gray-900'}`}>
                    {dateInfo.day}
                  </div>
                  <div className="text-xs text-gray-500">
                    {dateInfo.month}
                  </div>
                </div>

                {/* Day Items */}
                <div className="space-y-2">
                  {dayItems.map((item, itemIndex) => (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: itemIndex * 0.05 }}
                    >
                      {item.type === 'task' ? (
                        <TaskItem item={item} />
                      ) : (
                        <MeetingItem item={item} />
                      )}
                    </motion.div>
                  ))}
                  
                  {dayItems.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      <Calendar className="w-6 h-6 mx-auto mb-2" />
                      <p className="text-xs">No events</p>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TimelineView;
